# 麻雀重构专项一期-触达

## 1. 项目背景与目标

### 1.1 项目背景

当前麻雀系统存在以下问题：
- **代码分散**：T0实时触达和离线触达的实现分散在不同的类和方法中
- **渠道实现不统一**：各个渠道（短信、电销、Push、优惠券等）的实现代码不统一，维护成本高
- **业务逻辑耦合**：触达、频控、回执处理逻辑与业务策略耦合严重
- **扩展性差**：新增渠道或修改触达逻辑需要修改多处代码

### 1.2 项目目标

**核心目标**：将T0实时触达和离线触达的流程抽象收口到一个新的独立应用`xyf-touch-service`，实现：

1. **统一触达入口**：所有触达请求统一收口处理
2. **标准化流程**：触达、频控、回执处理标准化
3. **渠道插件化**：各渠道实现插件化，易于扩展
4. **配置化管理**：触达规则、频控策略配置化管理
5. **高可用性**：支持实时和批量两种触达模式

### 1.3 重构范围

#### 1.3.1 包含功能
- T0实时触达（普通触达 + 引擎触达）
- 离线触达（普通触达 + 引擎触达）
- 频控管理（事件级、触达级、分布式流控）
- 回执处理（短信、电销、Push、优惠券、AI外呼）
- 渠道管理（短信、电销、Push、优惠券、提额、生活权益、X天免息、AI外呼）

#### 1.3.2 不包含功能
- 策略管理（仍保留在原系统）
- 人群包管理（仍保留在原系统）
- 决策引擎（仍保留在原系统）
- 数据统计报表（仍保留在原系统）

## 2. 现状分析

### 2.1 当前架构问题

#### 2.1.1 代码分散问题
```
当前触达实现分散在：
├── StrategyEventDispatchServiceImpl.execSend()     # T0普通触达 ✅
├── StrategyEventDispatchServiceImpl.marketingSend() # T0引擎触达 + 离线引擎触达 ✅
├── AbstractStrategyDispatchService.dispatchHandler() # 离线普通触达 ✅
├── EventDispatchServiceImpl                        # T0触达 + 离线引擎触达共用 ✅
│   ├── sendSmsEvent() - 单用户短信                 # T0 + 离线引擎 ✅
│   ├── sendTeleEvent() - 单用户电销                # T0 + 离线引擎 ✅
│   ├── sendPushEvent() - 单用户推送                # T0 + 离线引擎 ✅
│   └── sendCouponEvent() - 单用户优惠券            # T0 + 离线引擎 ✅
├── BatchDispatchServiceImpl                        # ❌ T0不使用，仅离线普通触达 ✅
│   ├── sendSms() - 批量短信                        # 仅离线普通 ✅
│   ├── sendTele() - 批量电销                       # 仅离线普通 ✅
│   ├── sendPush() - 批量推送                       # 仅离线普通 ✅
│   └── sendCoupon() - 批量优惠券                   # 仅离线普通 ✅
└── 各渠道Service实现类                              # ❌ T0不涉及，仅离线普通触达 ✅
    ├── StrategyDispatchForSmsServiceImpl           # 短信渠道实现 ✅
    ├── StrategyDispatchForTeleServiceImpl          # 电销渠道实现 ✅
    ├── StrategyDispatchForCouponServiceImpl        # 优惠券渠道实现 ✅
    ├── StrategyDispatchForPushServiceImpl          # 推送渠道实现 ✅
    └── 特殊业务渠道实现类...                        # AI、提额、免息等特殊渠道 ✅
```

**关键发现**：
- **EventDispatchService**：T0触达和离线引擎触达都使用，处理单用户触达
- **BatchDispatchService**：❌ **T0触达完全不使用**，✅ **仅离线普通触达使用**，处理批量触达
- **各渠道Service实现类**：❌ **T0触达不涉及**，✅ **仅离线普通触达使用**，作为AbstractStrategyDispatchService的具体实现

#### 2.1.2 频控逻辑分散
```
频控实现分散在：
├── MqConsumeServiceImpl.isReject()                 # 事件级流控
│   └── 使用场景: ✅ T0触达（普通+引擎） ❌ 离线触达不使用
│
├── DispatchFlcService.dispatchFlc()                # 触达级流控
│   └── 使用场景: ✅ T0普通触达 ❌ T0引擎触达不使用 ❌ 离线触达不使用
│
├── DispatchFlcService.dispatchFlcLock()            # 分布式流控
│   └── 使用场景: ✅ T0引擎触达（内置在marketingSend中） ❌ T0普通触达不使用 ❌ 离线触达不使用
│
├── FlowCtrlCoreServiceImpl.flowCtrl()              # 离线批量流控
│   └── 使用场景: ❌ T0触达不使用 ✅ 离线普通触达
│
└── StrategyEventDispatchServiceImpl.marketingSend() # 引擎内置流控
    └── 使用场景: ✅ T0引擎触达 ✅ 离线引擎触达 ❌ 普通触达不使用
    └── 内置: dispatchFlcLock()分布式流控
```

**按触达类型的频控差异**：

| 触达类型 | 使用的频控方法 | 执行时机 | 特点 |
|---------|---------------|---------|------|
| **T0-普通触达** | `isReject()` + `dispatchFlc()` | MQ入口 + dispatch()前 | 两重保护，实时性强 |
| **T0-引擎触达** | `isReject()` + `marketingSend()`内置`dispatchFlcLock()` | MQ入口 + marketingSend()内部 | 两重保护，引擎专用 |
| **离线-普通触达** | `flowCtrl()` | 批处理过程中 | 批量流控，支持部分失败 |
| **离线-引擎触达** | `marketingSend()`内置流控 | marketingSend()内部 | 引擎内置，与T0引擎相同 |

#### 2.1.3 回执处理分散
```
回执处理分散在：
├── MqConsumeServiceImpl.smsReportProcess()         # 短信回执
├── MqConsumeServiceImpl.pushReportProcess()        # Push回执
├── MqConsumeServiceImpl.couponCallbackProcess()    # 优惠券回执
├── MqConsumeServiceImpl.aiCallbackProcess()        # AI外呼回执
└── 各渠道特定回执处理逻辑                           # 渠道回执逻辑
```

### 2.2 当前触达流程分析

#### 2.2.1 T0实时触达流程
```mermaid
graph TD
    A[MQ事件消息] --> B[MqConsumeServiceImpl.bizEventProcess]
    B --> C[事件级流控检查 isReject]
    C --> D{事件流控通过?}
    D -->|否| E[丢弃事件]
    D -->|是| F[StrategyEventDispatchServiceImpl.prescreen]
    F --> G[策略预筛选]
    G --> H[延迟队列]
    H --> I[StrategyEventDispatchServiceImpl.rescreen]
    I --> J[策略复筛选]
    J --> K{策略类型判断}
    K -->|普通策略| L[StrategyEventDispatchServiceImpl.dispatch]
    K -->|引擎策略| M[StrategyEventDispatchServiceImpl.rescreenWithEngine]
    L --> N[触达级流控检查 dispatchFlc]
    N --> O{触达流控通过?}
    O -->|否| P[终止触达]
    O -->|是| Q[execSend方法]
    M --> R[引擎决策]
    R --> S[marketingSend方法]
    S --> T[分布式流控检查 dispatchFlcLock]
    T --> U{分布式流控通过?}
    U -->|否| V[返回-999流控错误]
    U -->|是| W[EventDispatchServiceImpl渠道调用]
    Q --> W
    W --> X[第三方服务调用]
    X --> Y[回执处理]
```

#### 2.2.2 离线触达流程
```mermaid
graph TD
    A[XxlJob定时任务] --> B{任务类型}
    B -->|普通离线| C[AbstractStrategyDispatchService.execute]
    B -->|引擎离线| D[StrategyHandler.strategyDispatchOfflineEngine]

    subgraph "离线普通触达"
        C --> E[人群包查询]
        E --> F[AB测试分组]
        F --> G[批量流控检查 flowCtrl]
        G --> H[dispatchHandler抽象方法]
        H --> I[各渠道Service实现类]
        I --> J[BatchDispatchServiceImpl批量调用]
        J --> K[第三方服务调用 - 批量]
    end

    subgraph "离线引擎触达"
        D --> L[dispatch_user_delay表查询]
        L --> M[StrategyEventDispatchServiceImpl.marketingSend]
        M --> N[分布式流控检查 dispatchFlcLock]
        N --> O[EventDispatchServiceImpl渠道调用]
        O --> P[第三方服务调用 - 单用户]
    end

    K --> Q[回执处理]
    P --> Q
```

## 3. 目标架构设计

### 3.1 整体架构

#### 3.1.1 微服务架构图
```mermaid
graph TB
    subgraph "业务应用层"
        A[xyf-cdp 策略决策系统]
        B[其他业务系统]
    end

    subgraph "触达服务层"
        C[xyf-touch-service 触达服务]
    end

    subgraph "渠道服务层"
        D[短信服务]
        E[电销服务]
        F[Push服务]
        G[优惠券服务]
        H[AI外呼服务]
        I[其他渠道服务]
    end

    subgraph "基础设施层"
        J[Redis 缓存]
        K[MySQL 数据库]
        L[RabbitMQ 消息队列]
        M[RocketMQ 消息队列]
        N[Apollo 配置中心]
    end

    A --> C
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G
    C --> H
    C --> I
    C --> J
    C --> K
    C --> L
    C --> M
    C --> N
```

#### 3.1.2 应用边界定义

**xyf-touch-service 职责边界**：
- ✅ 触达请求统一接收和处理
- ✅ 频控策略执行和管理
- ✅ 渠道路由和调用管理
- ✅ 回执接收和状态更新
- ✅ 触达配置和规则管理
- ❌ 业务策略决策（保留在xyf-cdp）
- ❌ 人群包管理（保留在xyf-cdp）
- ❌ 数据统计报表（保留在xyf-cdp）

### 3.2 核心领域模型

#### 3.2.1 触达领域模型
```java
// 触达请求统一模型 - 整合所有触达入口参数
public class TouchRequest {
    // ===== 基础信息 =====
    private String requestId;           // 请求唯一标识 (新增字段)
    private TouchType touchType;        // 触达类型：T0_NORMAL, T0_ENGINE, OFFLINE_NORMAL, OFFLINE_ENGINE (新增字段)
    private TouchMode touchMode;        // 触达模式：SINGLE(单个), BATCH(批量) (新增字段)
    private Long timestamp;            // 请求时间戳 (新增字段)

    // ===== 策略信息 (源于DispatchDto + StrategyContext) =====
    private Long strategyId;           // 策略ID (源于DispatchDto.strategyId)
    private Long strategyGroupId;      // 策略分组ID (源于DispatchDto.strategyGroupId)
    private String strategyGroupName;  // 策略分组名称 (源于DispatchDto.strategyGroupName)
    private Long strategyChannelId;    // 策略渠道ID (源于DispatchDto.strategyChannelId)
    private String strategyExecId;     // 策略执行ID (源于DispatchDto.strategyExecId)
    private String detailTableNo;      // 明细表序号 (源于DispatchDto.detailTableNo)

    // ===== 用户信息 (源于CrowdDetailDo) =====
    private Long userId;               // 用户ID (源于CrowdDetailDo.userId)
    private String mobile;             // 手机号 (源于CrowdDetailDo.mobile)
    private String app;                // 应用标识 (源于CrowdDetailDo.app)
    private String innerApp;           // 内部应用标识 (源于CrowdDetailDo.innerApp)
    private String deviceId;           // 设备ID (源于CrowdDetailDo.deviceId)
    private String abNum;              // AB测试编号 (源于CrowdDetailDo.abNum)
    private Long crowdId;              // 人群包ID (源于CrowdDetailDo.crowdId)
    private Long crowdExecLogId;       // 人群执行日志ID (源于CrowdDetailDo.crowdExecLogId)

    // ===== 渠道信息 (源于StrategyMarketChannelEnum + StrategyMarketChannelDo) =====
    private TouchChannel channel;       // 触达渠道：SMS, VOICE, PUSH, COUPON等 (源于StrategyMarketChannelEnum)
    private String templateId;          // 模板ID (源于DispatchDto.strategyMarketChannelTemplateId)
    private String extInfo;             // 渠道扩展信息 (源于StrategyMarketChannelDo.extInfo)
    private String signatureKey;        // 短信签名 (源于DispatchDto.signatureKey)
    private String activityId;          // 优惠券活动ID (源于DispatchDto.activityId)
    private String nameTypeId;          // 名单类型ID (源于DispatchDto.nameTypeId)

    // ===== 业务事件信息 (源于BizEventVO) =====
    private String bizEventType;       // 业务事件类型 (源于DispatchDto.bizEventType)
    private String engineCode;         // 引擎代码 (源于BizEventVO.engineCode)
    private Map<String, Object> eventParamMap; // 事件参数 (源于DispatchDto.eventParamMap)

    // ===== 引擎信息 (源于marketingSend的groupName + detailInfo) =====
    private String engineGroupName;    // 引擎分组名称 (源于marketingSend.groupName)
    private Map<String, Object> engineDetail; // 引擎详细信息 (源于marketingSend.detailInfo)

    // ===== 模板参数 (源于dispatchHandler的templateParam) =====
    private List<Object> templateParams; // 模板参数列表 (源于dispatchHandler.templateParam)

    // ===== 批量处理信息 (源于dispatchHandler的batch) =====
    private List<TouchUserInfo> userBatch; // 用户批次信息 (源于dispatchHandler.batch转换)

    // ===== 流控信息 (源于DispatchDto.flowCtrlList) =====
    private List<FlowControlRule> flowCtrlRules; // 流控规则 (源于DispatchDto.flowCtrlList)

    // ===== 触达配置 =====
    private TouchConfig touchConfig;    // 触达配置 (新增字段)
    private String dispatchType;        // 触达类型：NOTIFY为通知不流控 (源于DispatchDto.dispatchType)
    private String bizType;             // 业务线类型 (源于DispatchDto.bizType)
}

// 触达用户信息 - 用于批量处理
public class TouchUserInfo {
    private Long userId;               // 用户ID
    private String mobile;             // 手机号
    private String app;                // 应用标识
    private String innerApp;           // 内部应用标识
    private String deviceId;           // 设备ID
    private String abNum;              // AB测试编号
    private Map<String, Object> extData; // 扩展数据
}

// 触达类型枚举
public enum TouchType {
    T0_NORMAL("T0_NORMAL", "T0普通触达"),
    T0_ENGINE("T0_ENGINE", "T0引擎触达"),
    OFFLINE_NORMAL("OFFLINE_NORMAL", "离线普通触达"),
    OFFLINE_ENGINE("OFFLINE_ENGINE", "离线引擎触达");

    private final String code;
    private final String desc;
}

// 触达模式枚举
public enum TouchMode {
    SINGLE("SINGLE", "单个触达"),
    BATCH("BATCH", "批量触达");

    private final String code;
    private final String desc;
}

// 触达渠道枚举 (映射StrategyMarketChannelEnum)
public enum TouchChannel {
    SMS("SMS", "短信"),
    VOICE("VOICE", "电销"),
    VOICE_NEW("VOICE_NEW", "新电销"),
    PUSH("PUSH", "Push推送"),
    COUPON("COUPON", "优惠券"),
    AI_PRONTO("AI_PRONTO", "AI即时触达"),
    INCREASE_AMOUNT("INCREASE_AMOUNT", "提额"),
    APP_BANNER("APP_BANNER", "APP横幅"),
    X_DAY_INTEREST_FREE("X_DAY_INTEREST_FREE", "X天免息"),
    LIFE_RIGHTS("LIFE_RIGHTS", "生活权益"),
    NONE("NONE", "不营销");

    private final String code;
    private final String desc;
}

// 触达响应统一模型
public class TouchResponse {
    private String requestId;          // 请求唯一标识
    private TouchStatus status;        // 触达状态：SUCCESS, FAILED, FLOW_CONTROLLED, PENDING
    private String batchNo;           // 批次号
    private String errorCode;         // 错误码
    private String errorMessage;      // 错误信息
    private Long timestamp;           // 响应时间戳
}

// 参数映射说明文档
## 触达入口参数映射详细说明

### 1. T0普通触达 (execSend) 参数映射

**原始方法签名：**
```java
private ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSend(
    DispatchDto dispatchDto,           // 分发参数DTO
    CrowdDetailDo crowdDetail,         // 用户明细信息
    StrategyMarketChannelEnum channelEnum,  // 渠道枚举
    StrategyMarketChannelDo channelDo, // 渠道配置
    BizEventVO bizEvent)               // 业务事件
```

**映射到TouchRequest的字段：**
- **DispatchDto → 策略信息**
  - `dispatchDto.strategyId` → `TouchRequest.strategyId`
  - `dispatchDto.strategyExecId` → `TouchRequest.strategyExecId`
  - `dispatchDto.strategyGroupId` → `TouchRequest.strategyGroupId`
  - `dispatchDto.strategyGroupName` → `TouchRequest.strategyGroupName`
  - `dispatchDto.strategyChannelId` → `TouchRequest.strategyChannelId`
  - `dispatchDto.strategyChannelXxlJobId` → `TouchRequest.strategyChannelXxlJobId`
  - `dispatchDto.strategyChannel` → `TouchRequest.strategyChannel`
  - `dispatchDto.strategyExecLogId` → `TouchRequest.strategyExecLogId`
  - `dispatchDto.strategyExecLogRetryId` → `TouchRequest.strategyExecLogRetryId`
  - `dispatchDto.detailTableNo` → `TouchRequest.detailTableNo`
  - `dispatchDto.messageId` → `TouchRequest.messageId`
  - `dispatchDto.strategyMarketChannelTemplateId` → `TouchRequest.templateId`
  - `dispatchDto.bizEventType` → `TouchRequest.bizEventType`
  - `dispatchDto.eventParamMap` → `TouchRequest.eventParamMap`
  - `dispatchDto.signatureKey` → `TouchRequest.signatureKey`
  - `dispatchDto.activityId` → `TouchRequest.activityId`
  - `dispatchDto.nameTypeId` → `TouchRequest.nameTypeId`
  - `dispatchDto.dispatchType` → `TouchRequest.dispatchType`
  - `dispatchDto.bizType` → `TouchRequest.bizType`
  - `dispatchDto.increaseAmtParamDto` → `TouchRequest.increaseAmtParamDto`
  - `dispatchDto.aiProntoChannelDto` → `TouchRequest.aiProntoChannelDto`

- **CrowdDetailDo → 用户信息**
  - `crowdDetail.userId` → `TouchRequest.userInfo.userId`
  - `crowdDetail.mobile` → `TouchRequest.userInfo.mobile`
  - `crowdDetail.app` → `TouchRequest.userInfo.app`
  - `crowdDetail.innerApp` → `TouchRequest.userInfo.innerApp`
  - `crowdDetail.deviceId` → `TouchRequest.userInfo.deviceId`
  - `crowdDetail.abNum` → `TouchRequest.userInfo.abNum`
  - `crowdDetail.appUserIdLast2` → `TouchRequest.userInfo.appUserIdLast2`
  - `crowdDetail.crowdId` → `TouchRequest.userInfo.crowdId`
  - `crowdDetail.crowdExecLogId` → `TouchRequest.userInfo.crowdExecLogId`
  - `crowdDetail.registerTime` → `TouchRequest.userInfo.registerTime`

- **StrategyMarketChannelEnum → 渠道信息**
  - `channelEnum` → `TouchRequest.channel` (通过枚举转换)

- **StrategyMarketChannelDo → 渠道配置**
  - `channelDo.extInfo` → `TouchRequest.channelExtInfo`

- **BizEventVO → 业务事件信息**
  - `bizEvent` → `TouchRequest.bizEventData` (转换为Map)
  - `bizEvent.traceId` → `TouchRequest.traceId`

### 2. T0引擎触达 + 离线引擎触达 (marketingSend) 参数映射

**原始方法签名：**
```java
public int marketingSend(
    DispatchDto dispatchDto,           // 分发参数DTO
    CrowdDetailDo crowdDetailDo,       // 用户明细信息
    StrategyMarketChannelEnum channelEnum,  // 渠道枚举
    String groupName,                  // 引擎分组名称
    Map detailInfo,                    // 引擎详细信息
    @Nullable BizEventVO bizEventVO)   // 业务事件(离线为null)
```

**映射到TouchRequest的字段：**
- **基础参数映射** (同T0普通触达的DispatchDto、CrowdDetailDo、StrategyMarketChannelEnum映射)
- **引擎特有参数**
  - `groupName` → `TouchRequest.engineGroupName`
  - `detailInfo` → `TouchRequest.engineDetail`
  - `bizEventVO.engineCode` → `TouchRequest.engineCode` (T0场景)
  - `bizEventVO == null` → `TouchRequest.touchType = OFFLINE_ENGINE` (离线场景)
  - `bizEventVO != null` → `TouchRequest.touchType = T0_ENGINE` (T0场景)

### 3. 离线普通触达 (dispatchHandler) 参数映射

**原始方法签名：**
```java
protected abstract <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(
    StrategyContext strategyContext,   // 策略上下文
    String app,                        // 应用标识
    String innerApp,                   // 内部应用标识
    List<CrowdDetailDo> batch,         // 用户批次
    List<T> templateParam)             // 模板参数
```

**映射到TouchRequest的字段：**
- **StrategyContext → 策略信息**
  - `strategyContext.strategyDo.id` → `TouchRequest.strategyId`
  - `strategyContext.strategyGroupDo.id` → `TouchRequest.strategyGroupId`
  - `strategyContext.strategyGroupDo.name` → `TouchRequest.strategyGroupName`
  - `strategyContext.strategyMarketChannelDo.id` → `TouchRequest.strategyChannelId`
  - `strategyContext.detailTableNo` → `TouchRequest.detailTableNo`
  - `strategyContext.strategyMarketChannelDo.marketChannel` → `TouchRequest.channel`
  - `strategyContext.strategyMarketChannelDo.templateId` → `TouchRequest.templateId`
  - `strategyContext.flowCtrlList` → `TouchRequest.flowControlConfig.rules`

- **批量用户信息**
  - `batch` → `TouchRequest.userList` (List<TouchUserInfo>)
  - `app` → 补充到每个用户的app字段
  - `innerApp` → 补充到每个用户的innerApp字段

- **模板参数**
  - `templateParam` → `TouchRequest.templateParams` (转换为Map)

- **批量处理信息**
  - 自动生成 → `TouchRequest.batchInfo.batchNo`
  - `batch.size()` → `TouchRequest.batchInfo.totalCount`
  - `TouchMode.BATCH` → `TouchRequest.touchMode`
  - `TouchType.OFFLINE_NORMAL` → `TouchRequest.touchType`

## 转换器实现说明

### 转换器类结构
```java
// 抽象转换器 - 使用Object类型，便于编译
@Component
public class TouchRequestConverter {
    // 使用Object类型的转换方法，包含详细的TODO注释
}

// 具体转换器 - 使用实际类型，需要引入外部依赖
@Component
public class ConcreteTouchRequestConverter {
    // 使用具体类型的转换方法，注释掉以避免编译错误
    // 实际使用时需要取消注释并引入相关依赖
}
```

### 使用方式
1. **开发阶段**: 使用TouchRequestConverter进行接口设计和测试
2. **集成阶段**: 引入cdp-domain依赖，启用ConcreteTouchRequestConverter
3. **生产阶段**: 完全替换为具体类型的转换器

// 频控规则模型
public class FlowControlRule {
    private Long ruleId;              // 规则ID
    private FlowControlType type;     // 频控类型：EVENT, TOUCH, DISTRIBUTED, BATCH
    private FlowControlScope scope;   // 频控范围：USER, STRATEGY, CHANNEL, GLOBAL
    private Integer limitTimes;       // 限制次数
    private Integer limitSeconds;     // 限制时间（秒）
    private RuleStatus status;        // 规则状态
}
```

#### 3.2.2 渠道插件模型
```java
// 渠道插件接口
public interface ChannelPlugin {
    TouchChannel getChannel();                    // 获取渠道类型
    TouchResponse send(TouchRequest request);     // 发送触达
    boolean isAvailable();                       // 渠道可用性检查
    ChannelConfig getConfig();                   // 获取渠道配置
}

// 渠道配置模型
public class ChannelConfig {
    private TouchChannel channel;        // 渠道类型
    private String serviceUrl;          // 服务地址
    private Integer timeout;            // 超时时间
    private Integer retryTimes;         // 重试次数
    private Map<String, String> params; // 渠道特定参数
    private Boolean enabled;            // 是否启用
}
```

### 3.3 分层架构设计

#### 3.3.1 应用分层结构
```
xyf-touch-service/
├── touch-api/                    # API层 - 对外接口
│   ├── controller/              # REST控制器
│   ├── dto/                     # 数据传输对象
│   └── facade/                  # 外观服务
├── touch-application/           # 应用层 - 业务编排
│   ├── service/                # 应用服务
│   ├── command/                # 命令对象
│   └── assembler/              # 对象转换器
├── touch-domain/               # 领域层 - 核心业务逻辑
│   ├── model/                  # 领域模型
│   ├── service/                # 领域服务
│   ├── repository/             # 仓储接口
│   └── event/                  # 领域事件
├── touch-infrastructure/       # 基础设施层 - 技术实现
│   ├── repository/             # 仓储实现
│   ├── mq/                     # 消息队列
│   ├── cache/                  # 缓存实现
│   ├── config/                 # 配置管理
│   └── channel/                # 渠道插件实现
└── touch-starter/              # 启动层 - 应用启动
    ├── config/                 # 启动配置
    └── TouchServiceApplication.java
```

#### 3.3.2 核心服务设计

**应用层服务**：
```java
// 触达应用服务 - 统一入口
@Service
public class TouchApplicationService {

    // 统一触达处理入口（支持T0实时和离线触达）
    public TouchResponse processTouch(TouchRequest request) {
        // 1. 请求验证
        // 2. 频控检查（根据触达类型选择不同频控策略）
        // 3. 渠道路由
        // 4. 触达执行
        // 5. 结果返回
    }

    // 批量触达处理
    public List<TouchResponse> processBatchTouch(List<TouchRequest> requests) {
        // 1. 批量验证
        // 2. 批量频控
        // 3. 分组路由
        // 4. 并行执行
        // 5. 结果汇总
    }

    // 回执统一处理
    public void processReceipt(ReceiptRequest receipt) {
        // 1. 回执验证
        // 2. 状态更新
        // 3. 业务回调
        // 4. 监控上报
    }
}
```

**领域层服务**：
```java
// 频控领域服务
@Service
public class FlowControlDomainService {

    // 事件级流控
    public boolean checkEventFlowControl(String eventType, Long userId);

    // 触达级流控
    public boolean checkTouchFlowControl(TouchRequest request);

    // 分布式流控
    public boolean acquireDistributedLock(String lockKey, int expireSeconds);

    // 批量流控
    public List<Long> filterByFlowControl(List<Long> userIds, FlowControlRule rule);
}

// 渠道路由领域服务
@Service
public class ChannelRoutingDomainService {

    // 渠道选择
    public ChannelPlugin selectChannel(TouchChannel channel);

    // 负载均衡
    public ChannelPlugin loadBalance(List<ChannelPlugin> channels);

    // 故障转移
    public ChannelPlugin failover(TouchChannel channel, Exception error);
}
```

## 4. 详细技术方案

### 4.1 触达流程统一化

#### 4.1.1 统一触达流程设计
```mermaid
graph TD
    A[触达请求] --> B[请求验证]
    B --> C[频控检查]
    C --> D{频控通过?}
    D -->|否| E[返回流控结果]
    D -->|是| F[渠道路由]
    F --> G[渠道插件调用]
    G --> H[第三方服务]
    H --> I[响应处理]
    I --> J[状态记录]
    J --> K[监控上报]
    K --> L[返回结果]

    M[回执消息] --> N[回执验证]
    N --> O[状态更新]
    O --> P[业务回调]
    P --> Q[监控上报]
```

#### 4.1.2 触达请求标准化
```java
// 统一触达请求处理器
@Component
public class TouchRequestProcessor {

    public TouchResponse process(TouchRequest request) {
        try {
            // 1. 请求验证
            validateRequest(request);

            // 2. 频控检查
            if (!flowControlService.check(request)) {
                return TouchResponse.flowControlled(request.getRequestId());
            }

            // 3. 渠道路由
            ChannelPlugin channel = channelRouter.route(request.getChannel());

            // 4. 触达执行
            TouchResponse response = channel.send(request);

            // 5. 状态记录
            touchRecordService.record(request, response);

            // 6. 监控上报
            monitorService.report(request, response);

            return response;

        } catch (Exception e) {
            return TouchResponse.failed(request.getRequestId(), e);
        }
    }
}
```

### 4.2 频控系统重构

#### 4.2.1 频控架构设计
```mermaid
graph TB
    subgraph "T0触达频控"
        A[事件级流控 isReject] --> B[触达级流控 dispatchFlc]
        A --> C[分布式流控 dispatchFlcLock]
        B --> D[T0普通触达]
        C --> E[T0引擎触达]
    end

    subgraph "离线触达频控"
        F[批量流控 flowCtrl] --> G[离线普通触达]
        H[引擎内置流控] --> I[离线引擎触达]
    end

    subgraph "统一频控服务"
        J[UnifiedFlowControlService] --> K[RealtimeFlowControlStrategy]
        J --> L[BatchFlowControlStrategy]
        J --> M[EngineFlowControlStrategy]
    end

    subgraph "存储层"
        N[Redis缓存] --> O[MySQL持久化]
    end

    K --> A
    K --> B
    K --> C
    L --> F
    M --> H
    A --> N
    F --> O
    C --> N
```

#### 4.2.2 统一频控服务
```java
// 统一频控服务
@Service
public class UnifiedFlowControlService {

    // 根据触达类型选择频控策略
    public FlowControlResult checkFlowControl(TouchRequest request) {
        FlowControlStrategy strategy = selectStrategy(request.getTouchType());
        return strategy.execute(request);
    }

    // 策略选择
    private FlowControlStrategy selectStrategy(TouchType touchType) {
        switch (touchType) {
            case T0_NORMAL:
            case T0_ENGINE:
                return realtimeFlowControlStrategy;
            case OFFLINE_NORMAL:
                return batchFlowControlStrategy;
            case OFFLINE_ENGINE:
                return engineFlowControlStrategy;
            default:
                throw new UnsupportedTouchTypeException(touchType);
        }
    }
}

// 实时频控策略
@Component
public class RealtimeFlowControlStrategy implements FlowControlStrategy {

    public FlowControlResult execute(TouchRequest request) {
        // 1. 事件级流控检查
        if (isEventLevelBlocked(request)) {
            return FlowControlResult.blocked("事件级流控拦截");
        }

        // 2. 根据触达类型选择不同的流控逻辑
        if (request.getTouchType() == TouchType.T0_NORMAL) {
            // T0普通触达：触达级流控
            return checkDispatchFlowControl(request);
        } else {
            // T0引擎触达：分布式流控
            return checkDistributedFlowControl(request);
        }
    }
}
```

### 4.3 渠道插件化架构

#### 4.3.1 渠道插件管理器
```java
// 渠道插件管理器
@Component
public class ChannelPluginManager {

    private final Map<TouchChannel, List<ChannelPlugin>> channelPlugins = new ConcurrentHashMap<>();

    // 注册渠道插件
    public void registerPlugin(ChannelPlugin plugin) {
        channelPlugins.computeIfAbsent(plugin.getChannel(), k -> new ArrayList<>()).add(plugin);
    }

    // 获取可用插件
    public ChannelPlugin getAvailablePlugin(TouchChannel channel) {
        List<ChannelPlugin> plugins = channelPlugins.get(channel);
        return plugins.stream()
                .filter(ChannelPlugin::isAvailable)
                .findFirst()
                .orElseThrow(() -> new ChannelNotAvailableException(channel));
    }

    // 健康检查
    @Scheduled(fixedRate = 30000)
    public void healthCheck() {
        channelPlugins.values().stream()
                .flatMap(List::stream)
                .forEach(plugin -> {
                    try {
                        plugin.isAvailable();
                    } catch (Exception e) {
                        log.warn("Channel plugin health check failed: {}", plugin.getChannel(), e);
                    }
                });
    }
}
```

#### 4.3.2 具体渠道插件实现
```java
// 短信渠道插件
@Component
public class SmsChannelPlugin implements ChannelPlugin {

    @Override
    public TouchChannel getChannel() {
        return TouchChannel.SMS;
    }

    @Override
    public TouchResponse send(TouchRequest request) {
        try {
            // 1. 参数转换
            SmsRequest smsRequest = convertToSmsRequest(request);

            // 2. 调用短信服务
            SmsResponse smsResponse = smsService.send(smsRequest);

            // 3. 结果转换
            return convertToTouchResponse(smsResponse, request.getRequestId());

        } catch (Exception e) {
            return TouchResponse.failed(request.getRequestId(), e.getMessage());
        }
    }

    @Override
    public boolean isAvailable() {
        return smsService.healthCheck();
    }

    @Override
    public ChannelConfig getConfig() {
        return channelConfigService.getConfig(TouchChannel.SMS);
    }
}

// 电销渠道插件
@Component
public class TeleChannelPlugin implements ChannelPlugin {

    @Override
    public TouchChannel getChannel() {
        return TouchChannel.VOICE;
    }

    @Override
    public TouchResponse send(TouchRequest request) {
        try {
            // 1. 参数转换
            TeleRequest teleRequest = convertToTeleRequest(request);

            // 2. 调用电销服务
            TeleResponse teleResponse = teleService.call(teleRequest);

            // 3. 结果转换
            return convertToTouchResponse(teleResponse, request.getRequestId());

        } catch (Exception e) {
            return TouchResponse.failed(request.getRequestId(), e.getMessage());
        }
    }

    @Override
    public boolean isAvailable() {
        return teleService.isServiceAvailable();
    }

    @Override
    public ChannelConfig getConfig() {
        return channelConfigService.getConfig(TouchChannel.VOICE);
    }
}
```

### 4.4 回执处理统一化

#### 4.4.1 回执处理架构
```mermaid
graph TD
    A[回执消息] --> B{消息类型}
    B -->|短信回执| C[SmsReceiptHandler]
    B -->|Push回执| D[PushReceiptHandler]
    B -->|优惠券回执| E[CouponReceiptHandler]
    B -->|AI外呼回执| F[AiCallReceiptHandler]

    C --> G[统一回执处理器]
    D --> G
    E --> G
    F --> G

    G --> H[状态更新]
    H --> I[业务回调]
    I --> J[监控上报]
```

#### 4.4.2 回执处理器实现
```java
// 统一回执处理接口
public interface ReceiptHandler {
    TouchChannel getChannel();
    void handle(ReceiptMessage message);
}

// 短信回执处理器
@Component
public class SmsReceiptHandler implements ReceiptHandler {

    @Override
    public TouchChannel getChannel() {
        return TouchChannel.SMS;
    }

    @Override
    public void handle(ReceiptMessage message) {
        try {
            // 1. 消息解析
            SmsReceiptMessage smsReceipt = parseMessage(message);

            // 2. 状态映射
            TouchStatus status = mapStatus(smsReceipt.getStatus());

            // 3. 更新触达记录
            touchRecordService.updateStatus(smsReceipt.getBatchNo(), status);

            // 4. 业务回调
            if (smsReceipt.needCallback()) {
                businessCallbackService.callback(smsReceipt);
            }

            // 5. 监控上报
            monitorService.reportReceipt(smsReceipt);

        } catch (Exception e) {
            log.error("Handle SMS receipt failed", e);
        }
    }
}

// 回执处理管理器
@Component
public class ReceiptHandlerManager {

    private final Map<TouchChannel, ReceiptHandler> handlers = new HashMap<>();

    @PostConstruct
    public void init() {
        // 自动注册所有回执处理器
        applicationContext.getBeansOfType(ReceiptHandler.class)
                .values()
                .forEach(handler -> handlers.put(handler.getChannel(), handler));
    }

    public void handleReceipt(TouchChannel channel, ReceiptMessage message) {
        ReceiptHandler handler = handlers.get(channel);
        if (handler != null) {
            handler.handle(message);
        } else {
            log.warn("No receipt handler found for channel: {}", channel);
        }
    }
}
```

### 4.5 配置管理系统

#### 4.5.1 配置层次结构
```
配置管理层次：
├── 全局配置
│   ├── 系统开关配置
│   ├── 监控配置
│   └── 基础设施配置
├── 渠道配置
│   ├── 渠道服务地址
│   ├── 超时重试配置
│   └── 渠道特定参数
├── 频控配置
│   ├── 频控规则配置
│   ├── 频控开关配置
│   └── 频控阈值配置
└── 业务配置
    ├── 模板配置
    ├── 路由规则配置
    └── 回调配置
```

#### 4.5.2 配置服务实现
```java
// 配置管理服务
@Service
public class TouchConfigService {

    // 获取渠道配置
    public ChannelConfig getChannelConfig(TouchChannel channel) {
        return apolloConfigService.getConfig("touch.channel." + channel.name().toLowerCase(), ChannelConfig.class);
    }

    // 获取频控配置
    public List<FlowControlRule> getFlowControlRules(TouchRequest request) {
        return flowControlRuleRepository.findActiveRules(
            request.getChannel(),
            request.getStrategyId(),
            request.getBizEventType()
        );
    }

    // 获取路由配置
    public RoutingConfig getRoutingConfig(TouchChannel channel) {
        return apolloConfigService.getConfig("touch.routing." + channel.name().toLowerCase(), RoutingConfig.class);
    }

    // 配置热更新
    @ApolloConfigChangeListener
    public void onConfigChange(ConfigChangeEvent changeEvent) {
        changeEvent.changedKeys().forEach(key -> {
            if (key.startsWith("touch.")) {
                refreshConfig(key);
            }
        });
    }
}
```

## 5. 数据库设计

### 5.1 核心数据表设计

#### 5.1.1 触达记录表
```sql
-- 触达记录主表
CREATE TABLE `touch_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` varchar(64) NOT NULL COMMENT '请求唯一标识',
  `batch_no` varchar(64) NOT NULL COMMENT '批次号',
  `touch_type` tinyint(4) NOT NULL COMMENT '触达类型：1-实时普通，2-实时引擎，3-离线普通，4-离线引擎',
  `channel` tinyint(4) NOT NULL COMMENT '触达渠道：1-短信，2-电销，3-Push，4-优惠券',
  `strategy_id` bigint(20) NOT NULL COMMENT '策略ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `biz_event_type` varchar(64) DEFAULT NULL COMMENT '业务事件类型',
  `template_params` text COMMENT '模板参数JSON',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待处理，1-成功，2-失败，3-流控',
  `error_code` varchar(32) DEFAULT NULL COMMENT '错误码',
  `error_message` varchar(512) DEFAULT NULL COMMENT '错误信息',
  `send_time` datetime NOT NULL COMMENT '发送时间',
  `callback_time` datetime DEFAULT NULL COMMENT '回执时间',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_id` (`request_id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_user_channel_time` (`user_id`, `channel`, `send_time`),
  KEY `idx_strategy_time` (`strategy_id`, `send_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='触达记录表';

-- 频控规则表
CREATE TABLE `flow_control_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(128) NOT NULL COMMENT '规则名称',
  `rule_type` tinyint(4) NOT NULL COMMENT '规则类型：1-事件级，2-触达级，3-分布式，4-批量',
  `scope_type` tinyint(4) NOT NULL COMMENT '范围类型：1-用户级，2-策略级，3-渠道级，4-全局级',
  `channel` tinyint(4) DEFAULT NULL COMMENT '渠道：1-短信，2-电销，3-Push，4-优惠券',
  `strategy_id` bigint(20) DEFAULT NULL COMMENT '策略ID',
  `biz_event_type` varchar(64) DEFAULT NULL COMMENT '业务事件类型',
  `limit_times` int(11) NOT NULL COMMENT '限制次数',
  `limit_seconds` int(11) NOT NULL COMMENT '限制时间（秒）',
  `priority` int(11) NOT NULL DEFAULT '0' COMMENT '优先级，数字越小优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type_scope` (`rule_type`, `scope_type`),
  KEY `idx_channel_strategy` (`channel`, `strategy_id`),
  KEY `idx_event_type` (`biz_event_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='频控规则表';

-- 渠道配置表
CREATE TABLE `channel_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `channel` tinyint(4) NOT NULL COMMENT '渠道：1-短信，2-电销，3-Push，4-优惠券',
  `config_key` varchar(64) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(32) NOT NULL COMMENT '配置类型：STRING, INTEGER, BOOLEAN, JSON',
  `description` varchar(256) DEFAULT NULL COMMENT '配置描述',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_channel_key` (`channel`, `config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道配置表';
```

#### 5.1.2 分表策略
```java
// 触达记录分表策略
@Component
public class TouchRecordShardingStrategy {

    // 按用户ID分表，支持8张表
    public String getTableName(Long userId) {
        int tableIndex = (int) (userId % 8);
        return "touch_record_" + tableIndex;
    }

    // 按时间范围查询时需要查询所有分表
    public List<String> getAllTableNames() {
        return IntStream.range(0, 8)
                .mapToObj(i -> "touch_record_" + i)
                .collect(Collectors.toList());
    }
}
```

### 5.2 数据迁移方案

#### 5.2.1 现有数据映射
```java
// 数据迁移服务
@Service
public class DataMigrationService {

    // 迁移用户触达明细数据
    public void migrateUserDispatchDetail() {
        // 从user_dispatch_detail_* 表迁移到 touch_record_* 表
        String sql = """
            INSERT INTO touch_record_%d (
                request_id, batch_no, touch_type, channel, strategy_id, user_id,
                status, send_time, callback_time, created_time, updated_time
            )
            SELECT
                CONCAT('migrate_', id) as request_id,
                batch_no,
                CASE
                    WHEN source_type = 'T0_NORMAL' THEN 1
                    WHEN source_type = 'T0_ENGINE' THEN 2
                    WHEN source_type = 'OFFLINE_NORMAL' THEN 3
                    WHEN source_type = 'OFFLINE_ENGINE' THEN 4
                END as touch_type,
                market_channel as channel,
                strategy_id,
                user_id,
                status,
                send_time,
                callback_time,
                created_time,
                updated_time
            FROM user_dispatch_detail_%d
            WHERE created_time >= ?
            """;

        // 分表迁移
        for (int i = 0; i < 8; i++) {
            jdbcTemplate.update(String.format(sql, i, i), startTime);
        }
    }
}
```

## 6. 接口设计

### 6.1 对外API接口

#### 6.1.1 触达API
```java
// 触达控制器
@RestController
@RequestMapping("/api/touch")
public class TouchController {

    // 单个触达请求
    @PostMapping("/send")
    public ApiResponse<TouchResponse> send(@RequestBody @Valid TouchRequest request) {
        TouchResponse response = touchApplicationService.processRealtimeTouch(request);
        return ApiResponse.success(response);
    }

    // 批量触达请求
    @PostMapping("/batch-send")
    public ApiResponse<List<TouchResponse>> batchSend(@RequestBody @Valid List<TouchRequest> requests) {
        List<TouchResponse> responses = touchApplicationService.processBatchTouch(requests);
        return ApiResponse.success(responses);
    }

    // 查询触达状态
    @GetMapping("/status/{requestId}")
    public ApiResponse<TouchStatus> getStatus(@PathVariable String requestId) {
        TouchStatus status = touchQueryService.getStatus(requestId);
        return ApiResponse.success(status);
    }

    // 查询触达记录
    @GetMapping("/records")
    public ApiResponse<PageResult<TouchRecord>> getRecords(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long strategyId,
            @RequestParam(required = false) TouchChannel channel,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "20") int pageSize) {

        PageResult<TouchRecord> records = touchQueryService.getRecords(
            userId, strategyId, channel, pageNum, pageSize);
        return ApiResponse.success(records);
    }
}
```

#### 6.1.2 回执API
```java
// 回执控制器
@RestController
@RequestMapping("/api/receipt")
public class ReceiptController {

    // 短信回执
    @PostMapping("/sms")
    public ApiResponse<Void> smsReceipt(@RequestBody SmsReceiptRequest request) {
        receiptApplicationService.processSmsReceipt(request);
        return ApiResponse.success();
    }

    // Push回执
    @PostMapping("/push")
    public ApiResponse<Void> pushReceipt(@RequestBody PushReceiptRequest request) {
        receiptApplicationService.processPushReceipt(request);
        return ApiResponse.success();
    }

    // 优惠券回执
    @PostMapping("/coupon")
    public ApiResponse<Void> couponReceipt(@RequestBody CouponReceiptRequest request) {
        receiptApplicationService.processCouponReceipt(request);
        return ApiResponse.success();
    }

    // AI外呼回执
    @PostMapping("/ai-call")
    public ApiResponse<Void> aiCallReceipt(@RequestBody AiCallReceiptRequest request) {
        receiptApplicationService.processAiCallReceipt(request);
        return ApiResponse.success();
    }
}
```

### 6.2 内部服务接口

#### 6.2.1 频控服务接口
```java
// 频控服务接口
@FeignClient(name = "touch-service", path = "/internal/flow-control")
public interface FlowControlService {

    // 检查事件流控
    @PostMapping("/check-event")
    FlowControlResult checkEventFlowControl(@RequestBody EventFlowControlRequest request);

    // 检查触达流控
    @PostMapping("/check-touch")
    FlowControlResult checkTouchFlowControl(@RequestBody TouchFlowControlRequest request);

    // 批量流控检查
    @PostMapping("/batch-check")
    List<FlowControlResult> batchCheck(@RequestBody List<TouchFlowControlRequest> requests);

    // 获取流控规则
    @GetMapping("/rules")
    List<FlowControlRule> getRules(@RequestParam TouchChannel channel,
                                  @RequestParam(required = false) Long strategyId);
}
```

#### 6.2.2 渠道服务接口
```java
// 渠道服务接口
@FeignClient(name = "touch-service", path = "/internal/channel")
public interface ChannelService {

    // 发送触达
    @PostMapping("/send")
    TouchResponse send(@RequestBody ChannelSendRequest request);

    // 批量发送
    @PostMapping("/batch-send")
    List<TouchResponse> batchSend(@RequestBody List<ChannelSendRequest> requests);

    // 渠道健康检查
    @GetMapping("/health/{channel}")
    ChannelHealthStatus getHealth(@PathVariable TouchChannel channel);

    // 获取渠道配置
    @GetMapping("/config/{channel}")
    ChannelConfig getConfig(@PathVariable TouchChannel channel);
}
```

## 7. 部署架构

### 7.1 微服务部署架构

#### 7.1.1 Kubernetes部署配置
```yaml
# touch-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: touch-service
  namespace: touch
spec:
  replicas: 3
  selector:
    matchLabels:
      app: touch-service
  template:
    metadata:
      labels:
        app: touch-service
    spec:
      containers:
      - name: touch-service
        image: touch-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: APOLLO_META
          value: "http://apollo-config-service:8080"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10

---
# touch-service-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: touch-service
  namespace: touch
spec:
  selector:
    app: touch-service
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
```

#### 7.1.2 配置管理
```yaml
# apollo配置示例
# touch-service.yml
touch:
  # 渠道配置
  channel:
    sms:
      serviceUrl: "http://sms.xinfei.io"
      timeout: 30000
      retryTimes: 3
      enabled: true
    voice:
      serviceUrl: "http://telemkt.xinfei.io"
      timeout: 60000
      retryTimes: 2
      enabled: true
    push:
      serviceUrl: "http://sms.xinfei.io"
      timeout: 30000
      retryTimes: 3
      enabled: true
    coupon:
      serviceUrl: "http://userassetcore.xinfei.io"
      timeout: 30000
      retryTimes: 3
      enabled: true

  # 频控配置
  flowControl:
    event:
      enabled: true
      defaultLimitSeconds: 600
    touch:
      enabled: true
      lockTimeoutSeconds: 360
    distributed:
      enabled: true
      lockPrefix: "touch:lock:"

  # 线程池配置
  executor:
    corePoolSize: 20
    maxPoolSize: 100
    queueCapacity: 1000
    keepAliveSeconds: 60
```

### 7.2 监控和告警

#### 7.2.1 监控指标设计
```java
// 监控指标服务
@Service
public class TouchMetricsService {

    private final MeterRegistry meterRegistry;

    // 触达成功率
    public void recordTouchSuccess(TouchChannel channel) {
        Counter.builder("touch.success")
                .tag("channel", channel.name())
                .register(meterRegistry)
                .increment();
    }

    // 触达失败率
    public void recordTouchFailure(TouchChannel channel, String errorCode) {
        Counter.builder("touch.failure")
                .tag("channel", channel.name())
                .tag("error_code", errorCode)
                .register(meterRegistry)
                .increment();
    }

    // 频控拦截率
    public void recordFlowControl(TouchChannel channel, String ruleType) {
        Counter.builder("touch.flow_control")
                .tag("channel", channel.name())
                .tag("rule_type", ruleType)
                .register(meterRegistry)
                .increment();
    }

    // 触达耗时
    public void recordTouchLatency(TouchChannel channel, long latencyMs) {
        Timer.builder("touch.latency")
                .tag("channel", channel.name())
                .register(meterRegistry)
                .record(latencyMs, TimeUnit.MILLISECONDS);
    }

    // 渠道可用性
    public void recordChannelHealth(TouchChannel channel, boolean isHealthy) {
        Gauge.builder("touch.channel.health")
                .tag("channel", channel.name())
                .register(meterRegistry, isHealthy ? 1 : 0);
    }
}
```

#### 7.2.2 告警规则配置
```yaml
# prometheus告警规则
groups:
- name: touch-service-alerts
  rules:
  # 触达成功率告警
  - alert: TouchSuccessRateLow
    expr: |
      (
        rate(touch_success_total[5m]) /
        (rate(touch_success_total[5m]) + rate(touch_failure_total[5m]))
      ) < 0.95
    for: 2m
    labels:
      severity: warning
      service: touch-service
    annotations:
      summary: "触达成功率过低"
      description: "{{ $labels.channel }}渠道触达成功率低于95%，当前值：{{ $value }}"

  # 频控拦截率告警
  - alert: FlowControlRateHigh
    expr: |
      rate(touch_flow_control_total[5m]) /
      (rate(touch_success_total[5m]) + rate(touch_failure_total[5m]) + rate(touch_flow_control_total[5m])) > 0.3
    for: 5m
    labels:
      severity: warning
      service: touch-service
    annotations:
      summary: "频控拦截率过高"
      description: "{{ $labels.channel }}渠道频控拦截率超过30%，当前值：{{ $value }}"

  # 渠道不可用告警
  - alert: ChannelUnavailable
    expr: touch_channel_health == 0
    for: 1m
    labels:
      severity: critical
      service: touch-service
    annotations:
      summary: "渠道不可用"
      description: "{{ $labels.channel }}渠道不可用"

  # 触达延迟告警
  - alert: TouchLatencyHigh
    expr: histogram_quantile(0.95, rate(touch_latency_seconds_bucket[5m])) > 10
    for: 3m
    labels:
      severity: warning
      service: touch-service
    annotations:
      summary: "触达延迟过高"
      description: "{{ $labels.channel }}渠道95%触达延迟超过10秒"
```

## 8. 迁移实施方案

### 8.1 迁移策略

#### 8.1.1 分阶段迁移计划
```mermaid
gantt
    title 触达服务迁移时间线
    dateFormat  YYYY-MM-DD
    section 准备阶段
    需求分析和设计     :done, design, 2025-01-01, 2025-01-15
    技术方案评审       :done, review, 2025-01-16, 2025-01-20

    section 开发阶段
    基础框架搭建       :dev1, 2025-01-21, 2025-02-05
    核心功能开发       :dev2, after dev1, 15d
    渠道插件开发       :dev3, after dev1, 20d

    section 测试阶段
    单元测试          :test1, after dev2, 5d
    集成测试          :test2, after dev3, 7d
    性能测试          :test3, after test2, 5d

    section 部署阶段
    测试环境部署       :deploy1, after test1, 3d
    预生产环境部署     :deploy2, after test3, 3d
    生产环境部署       :deploy3, after deploy2, 5d

    section 迁移阶段
    灰度迁移          :migrate1, after deploy3, 10d
    全量迁移          :migrate2, after migrate1, 5d
    旧系统下线        :migrate3, after migrate2, 5d
```

#### 8.1.2 灰度迁移策略
```java
// 灰度迁移控制器
@Component
public class MigrationController {

    // 灰度策略配置
    @Value("${migration.gray.strategies:}")
    private List<Long> grayStrategies;

    @Value("${migration.gray.percentage:0}")
    private int grayPercentage;

    // 判断是否使用新服务
    public boolean shouldUseNewService(TouchRequest request) {
        // 1. 策略白名单
        if (grayStrategies.contains(request.getStrategyId())) {
            return true;
        }

        // 2. 按百分比灰度
        if (grayPercentage > 0) {
            int hash = Math.abs(request.getUserId().hashCode() % 100);
            return hash < grayPercentage;
        }

        return false;
    }

    // 双写验证
    public void dualWrite(TouchRequest request) {
        // 同时调用新旧服务，比较结果
        CompletableFuture<TouchResponse> oldServiceFuture =
            CompletableFuture.supplyAsync(() -> oldTouchService.send(request));

        CompletableFuture<TouchResponse> newServiceFuture =
            CompletableFuture.supplyAsync(() -> newTouchService.send(request));

        CompletableFuture.allOf(oldServiceFuture, newServiceFuture)
            .thenRun(() -> {
                try {
                    TouchResponse oldResponse = oldServiceFuture.get();
                    TouchResponse newResponse = newServiceFuture.get();
                    compareResults(request, oldResponse, newResponse);
                } catch (Exception e) {
                    log.error("Dual write comparison failed", e);
                }
            });
    }
}
```

### 8.2 数据一致性保证

#### 8.2.1 数据同步策略
```java
// 数据同步服务
@Service
public class DataSyncService {

    // 实时数据同步
    @EventListener
    public void syncTouchRecord(TouchRecordEvent event) {
        try {
            // 同步到新表结构
            TouchRecord newRecord = convertToNewFormat(event.getRecord());
            newTouchRecordRepository.save(newRecord);

            // 同步到旧表结构（兼容期间）
            UserDispatchDetail oldRecord = convertToOldFormat(event.getRecord());
            oldUserDispatchDetailRepository.save(oldRecord);

        } catch (Exception e) {
            log.error("Data sync failed", e);
            // 发送到死信队列重试
            deadLetterService.send(event);
        }
    }

    // 数据一致性校验
    @Scheduled(fixedRate = 300000) // 5分钟执行一次
    public void checkDataConsistency() {
        LocalDateTime checkTime = LocalDateTime.now().minusMinutes(10);

        // 检查最近10分钟的数据一致性
        List<TouchRecord> newRecords = newTouchRecordRepository.findByCreatedTimeAfter(checkTime);
        List<UserDispatchDetail> oldRecords = oldUserDispatchDetailRepository.findByCreatedTimeAfter(checkTime);

        // 比较数据一致性
        Set<String> newRequestIds = newRecords.stream()
                .map(TouchRecord::getRequestId)
                .collect(Collectors.toSet());

        Set<String> oldRequestIds = oldRecords.stream()
                .map(record -> "migrate_" + record.getId())
                .collect(Collectors.toSet());

        // 找出不一致的数据
        Set<String> missingInNew = new HashSet<>(oldRequestIds);
        missingInNew.removeAll(newRequestIds);

        Set<String> missingInOld = new HashSet<>(newRequestIds);
        missingInOld.removeAll(oldRequestIds);

        if (!missingInNew.isEmpty() || !missingInOld.isEmpty()) {
            log.warn("Data inconsistency detected. Missing in new: {}, Missing in old: {}",
                    missingInNew.size(), missingInOld.size());

            // 发送告警
            alertService.sendDataInconsistencyAlert(missingInNew, missingInOld);
        }
    }
}
```

### 8.3 回滚方案

#### 8.3.1 快速回滚机制
```java
// 回滚控制服务
@Service
public class RollbackService {

    // 紧急回滚开关
    @Value("${migration.emergency.rollback:false}")
    private boolean emergencyRollback;

    // 回滚到旧服务
    public TouchResponse rollbackToOldService(TouchRequest request) {
        log.warn("Rolling back to old service for request: {}", request.getRequestId());

        try {
            // 调用旧服务
            return oldTouchService.send(request);
        } catch (Exception e) {
            log.error("Rollback to old service failed", e);
            throw new RollbackException("Both new and old service failed", e);
        }
    }

    // 检查回滚条件
    public boolean shouldRollback(TouchRequest request, Exception error) {
        // 1. 紧急回滚开关
        if (emergencyRollback) {
            return true;
        }

        // 2. 特定错误类型回滚
        if (error instanceof ChannelNotAvailableException ||
            error instanceof FlowControlException) {
            return true;
        }

        // 3. 连续失败率过高
        double failureRate = getRecentFailureRate(request.getChannel());
        if (failureRate > 0.1) { // 失败率超过10%
            return true;
        }

        return false;
    }

    // 获取最近失败率
    private double getRecentFailureRate(TouchChannel channel) {
        // 查询最近5分钟的成功失败统计
        LocalDateTime startTime = LocalDateTime.now().minusMinutes(5);

        long successCount = touchRecordRepository.countByChannelAndStatusAndCreatedTimeAfter(
                channel, TouchStatus.SUCCESS, startTime);

        long failureCount = touchRecordRepository.countByChannelAndStatusAndCreatedTimeAfter(
                channel, TouchStatus.FAILED, startTime);

        long totalCount = successCount + failureCount;
        return totalCount > 0 ? (double) failureCount / totalCount : 0.0;
    }
}
```

## 9. 风险评估与应对

### 9.1 技术风险

#### 9.1.1 性能风险
**风险描述**：新服务性能不达预期，影响触达效率
**应对措施**：
- 压力测试验证性能指标
- 异步处理提升吞吐量
- 缓存优化减少数据库压力
- 监控告警及时发现问题

#### 9.1.2 稳定性风险
**风险描述**：新服务稳定性问题导致触达中断
**应对措施**：
- 完善的单元测试和集成测试
- 灰度发布降低影响范围
- 快速回滚机制
- 多环境验证

#### 9.1.3 数据一致性风险
**风险描述**：迁移过程中数据不一致
**应对措施**：
- 双写机制保证数据同步
- 定时校验数据一致性
- 数据修复工具
- 详细的操作日志

### 9.2 业务风险

#### 9.2.1 功能缺失风险
**风险描述**：新服务功能不完整影响业务
**应对措施**：
- 详细的功能对比分析
- 完整的测试用例覆盖
- 业务方验收确认
- 功能开关控制

#### 9.2.2 兼容性风险
**风险描述**：接口变更影响上游系统
**应对措施**：
- 保持接口向后兼容
- 版本化API设计
- 充分的联调测试
- 文档同步更新

## 10. 关键发现与设计要点

### 10.1 频控机制重要发现

通过深入分析现有代码，发现了当前频控机制的重要特征：

#### 10.1.1 T0触达频控差异化
- **T0-普通触达**：使用`isReject()` + `dispatchFlc()`两重保护
- **T0-引擎触达**：使用`isReject()` + `marketingSend()`内置`dispatchFlcLock()`
- **关键差异**：T0-引擎触达不经过`dispatch()`方法，因此不使用`dispatchFlc()`流控

#### 10.1.2 触达实现层级分离
- **EventDispatchService**：T0触达和离线引擎触达共用，处理单用户触达
- **BatchDispatchService**：仅离线普通触达使用，处理批量触达
- **各渠道Service实现类**：仅离线普通触达使用，T0触达完全不涉及

#### 10.1.3 重构设计原则
基于以上发现，重构设计必须：
1. **保持频控差异化**：不同触达类型使用不同的频控策略
2. **统一服务接口**：通过策略模式支持不同的频控实现
3. **向下兼容**：保持现有频控逻辑的兼容性
4. **配置统一**：将分散的频控配置统一管理

## 11. 总结

### 11.1 项目收益

#### 10.1.1 技术收益
- **代码统一**：触达逻辑统一收口，消除重复代码
- **架构清晰**：分层架构明确，职责边界清楚
- **扩展性强**：插件化设计，易于新增渠道
- **维护性好**：配置化管理，降低维护成本

#### 10.1.2 业务收益
- **效率提升**：统一的触达流程，提升开发效率
- **质量保证**：标准化的频控和回执处理
- **监控完善**：全链路监控，问题快速定位
- **成本降低**：减少重复开发，降低维护成本

### 10.2 后续规划

#### 10.2.1 功能增强
- 智能路由：基于渠道质量自动选择最优渠道
- 个性化触达：基于用户画像的个性化内容推送
- 实时决策：集成实时决策引擎，动态调整触达策略
- 多媒体支持：支持图片、视频等多媒体内容触达

#### 10.2.2 技术演进
- 云原生改造：容器化部署，支持弹性伸缩
- 服务网格：引入Istio等服务网格技术
- 事件驱动：基于事件驱动架构的异步处理
- AI集成：集成AI能力，智能化触达决策

---

**文档版本**: v1.1
**创建时间**: 2025-06-20
**更新时间**: 2025-06-23
**维护人员**: CDP开发团队
**审核状态**: 待审核
**更新说明**: 基于代码深度分析，修正了频控机制和触达实现的分类，特别是T0-引擎触达的频控流程
```
```
```