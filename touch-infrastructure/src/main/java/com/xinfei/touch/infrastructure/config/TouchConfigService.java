package com.xinfei.touch.infrastructure.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.xinfei.touch.application.service.ChannelConfig;
import com.xinfei.touch.domain.model.TouchChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 触达配置服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class TouchConfigService {
    
    private final Map<TouchChannel, ChannelConfig> channelConfigCache = new ConcurrentHashMap<>();
    private Config apolloConfig;
    
    @PostConstruct
    public void init() {
        apolloConfig = ConfigService.getAppConfig();
        loadAllChannelConfigs();
    }
    
    /**
     * 获取渠道配置
     * 
     * @param channel 触达渠道
     * @return 渠道配置
     */
    public ChannelConfig getChannelConfig(TouchChannel channel) {
        return channelConfigCache.computeIfAbsent(channel, this::loadChannelConfig);
    }
    
    /**
     * 加载所有渠道配置
     */
    private void loadAllChannelConfigs() {
        for (TouchChannel channel : TouchChannel.values()) {
            loadChannelConfig(channel);
        }
    }
    
    /**
     * 加载渠道配置
     */
    private ChannelConfig loadChannelConfig(TouchChannel channel) {
        try {
            String prefix = "touch.channel." + channel.getIdentifier();
            
            ChannelConfig config = new ChannelConfig();
            config.setChannel(channel);
            config.setServiceUrl(apolloConfig.getProperty(prefix + ".service-url", ""));
            config.setTimeout(apolloConfig.getIntProperty(prefix + ".timeout", 30000));
            config.setRetryTimes(apolloConfig.getIntProperty(prefix + ".retry-times", 3));
            config.setPoolSize(apolloConfig.getIntProperty(prefix + ".pool-size", 10));
            config.setEnabled(apolloConfig.getBooleanProperty(prefix + ".enabled", true));
            config.setWeight(apolloConfig.getIntProperty(prefix + ".weight", 100));
            
            channelConfigCache.put(channel, config);
            
            log.info("加载渠道配置: channel={}, config={}", channel, config);
            
            return config;
            
        } catch (Exception e) {
            log.error("加载渠道配置失败: channel={}", channel, e);
            return ChannelConfig.createDefault(channel);
        }
    }
    
    /**
     * 配置热更新
     */
    @ApolloConfigChangeListener
    public void onConfigChange(ConfigChangeEvent changeEvent) {
        changeEvent.changedKeys().forEach(key -> {
            if (key.startsWith("touch.channel.")) {
                refreshChannelConfig(key);
            }
        });
    }
    
    /**
     * 刷新渠道配置
     */
    private void refreshChannelConfig(String configKey) {
        try {
            // 解析配置键，确定是哪个渠道的配置
            String[] parts = configKey.split("\\.");
            if (parts.length >= 3) {
                String channelIdentifier = parts[2];
                TouchChannel channel = TouchChannel.getByIdentifier(channelIdentifier);
                
                // 重新加载配置
                loadChannelConfig(channel);
                
                log.info("刷新渠道配置: channel={}, configKey={}", channel, configKey);
            }
        } catch (Exception e) {
            log.error("刷新渠道配置失败: configKey={}", configKey, e);
        }
    }
    
    /**
     * 获取频控配置
     */
    public boolean isFlowControlEnabled(String type) {
        return apolloConfig.getBooleanProperty("touch.flow-control." + type + ".enabled", true);
    }
    
    /**
     * 获取线程池配置
     */
    public int getExecutorCorePoolSize() {
        return apolloConfig.getIntProperty("touch.executor.core-pool-size", 20);
    }
    
    public int getExecutorMaxPoolSize() {
        return apolloConfig.getIntProperty("touch.executor.max-pool-size", 100);
    }
    
    public int getExecutorQueueCapacity() {
        return apolloConfig.getIntProperty("touch.executor.queue-capacity", 1000);
    }
}
