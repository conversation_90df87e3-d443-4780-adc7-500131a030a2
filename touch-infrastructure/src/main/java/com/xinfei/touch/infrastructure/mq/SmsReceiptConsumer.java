package com.xinfei.touch.infrastructure.mq;

import com.alibaba.fastjson.JSON;
import com.xinfei.touch.application.dto.SmsReportDTO;
import com.xinfei.touch.application.service.ReceiptApplicationService;
import com.xinfei.touch.infrastructure.mq.dto.SmsReportVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 短信回执MQ消费者
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmsReceiptConsumer {
    
    private final ReceiptApplicationService receiptApplicationService;
    
    /**
     * 短信回执消息处理
     * 
     * 队列配置：sms_supplier_report_callback
     * 交换机：exchange_report_callback_topic
     * 路由键：sms_center_callback_app_xyf-cdp
     */
    @RabbitListener(queues = "${sms.report.queue.name:sms_supplier_report_callback}")
    public void smsReportCallback(String message) {
        try {
            log.info("收到短信回执消息: {}", message);
            
            // 解析消息
            List<SmsReportVO> smsReportList = JSON.parseArray(message, SmsReportVO.class);
            if (smsReportList == null || smsReportList.isEmpty()) {
                log.warn("短信回执消息为空: {}", message);
                return;
            }
            
            // 转换为应用层DTO
            List<SmsReportDTO> smsReportDTOList = smsReportList.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            // 统一回执处理
            receiptApplicationService.processSmsReceipts(smsReportDTOList);
            
            log.info("短信回执处理完成: count={}", smsReportDTOList.size());

        } catch (Exception e) {
            log.error("短信回执处理异常: message={}", message, e);
            // 注意：这里不抛出异常，避免消息重复消费
        }
    }

    /**
     * 转换为应用层DTO
     */
    private SmsReportDTO convertToDTO(SmsReportVO vo) {
        SmsReportDTO dto = new SmsReportDTO();
        BeanUtils.copyProperties(vo, dto);
        return dto;
    }
}
