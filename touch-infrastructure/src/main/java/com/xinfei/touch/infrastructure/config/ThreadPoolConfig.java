package com.xinfei.touch.infrastructure.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableAsync
@RequiredArgsConstructor
public class ThreadPoolConfig {
    
    private final TouchConfigService touchConfigService;
    
    /**
     * 触达处理线程池
     */
    @Bean("touchExecutor")
    public Executor touchExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(touchConfigService.getExecutorCorePoolSize());
        executor.setMaxPoolSize(touchConfigService.getExecutorMaxPoolSize());
        executor.setQueueCapacity(touchConfigService.getExecutorQueueCapacity());
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("touch-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }
    
    /**
     * 监控上报线程池
     */
    @Bean("monitorExecutor")
    public Executor monitorExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("monitor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        executor.initialize();
        return executor;
    }
}
