package com.xinfei.touch.infrastructure.external;

import com.xinfei.touch.infrastructure.external.dto.SmsRequest;
import com.xinfei.touch.infrastructure.external.dto.SmsResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * 短信服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmsService {
    
    private final WebClient webClient;
    
    /**
     * 发送短信
     * 
     * @param request 短信请求
     * @return 短信响应
     */
    public SmsResponse send(SmsRequest request) {
        try {
            log.info("发送短信: requestId={}, mobile={}", request.getRequestId(), request.getMobile());
            
            // TODO: 调用实际的短信服务
            // 这里模拟调用外部短信服务
            SmsResponse response = new SmsResponse();
            response.setSuccess(true);
            response.setBatchNo("SMS_" + System.currentTimeMillis());
            response.setMessage("短信发送成功");
            
            return response;
            
        } catch (Exception e) {
            log.error("短信发送失败: requestId={}", request.getRequestId(), e);
            
            SmsResponse response = new SmsResponse();
            response.setSuccess(false);
            response.setErrorCode("SMS_SEND_ERROR");
            response.setErrorMessage("短信发送失败: " + e.getMessage());
            
            return response;
        }
    }
    
    /**
     * 健康检查
     * 
     * @return 是否健康
     */
    public boolean healthCheck() {
        try {
            // TODO: 实现健康检查逻辑
            return true;
        } catch (Exception e) {
            log.warn("短信服务健康检查失败", e);
            return false;
        }
    }
}
