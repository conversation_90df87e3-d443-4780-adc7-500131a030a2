package com.xinfei.touch.domain.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 触达记录领域模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class TouchRecord {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 请求唯一标识
     */
    private String requestId;
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 触达类型
     */
    private TouchType touchType;
    
    /**
     * 触达渠道
     */
    private TouchChannel channel;
    
    /**
     * 策略ID
     */
    private Long strategyId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 业务事件类型
     */
    private String bizEventType;
    
    /**
     * 模板参数
     */
    private Map<String, Object> templateParams;
    
    /**
     * 状态
     */
    private TouchStatus status;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    
    /**
     * 回执时间
     */
    private LocalDateTime callbackTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 创建触达记录
     */
    public static TouchRecord create(String requestId, String batchNo, TouchType touchType, 
                                   TouchChannel channel, Long strategyId, Long userId, 
                                   String bizEventType, Map<String, Object> templateParams) {
        TouchRecord record = new TouchRecord();
        record.setRequestId(requestId);
        record.setBatchNo(batchNo);
        record.setTouchType(touchType);
        record.setChannel(channel);
        record.setStrategyId(strategyId);
        record.setUserId(userId);
        record.setBizEventType(bizEventType);
        record.setTemplateParams(templateParams);
        record.setStatus(TouchStatus.PENDING);
        record.setSendTime(LocalDateTime.now());
        record.setCreatedTime(LocalDateTime.now());
        record.setUpdatedTime(LocalDateTime.now());
        return record;
    }
    
    /**
     * 更新状态
     */
    public void updateStatus(TouchStatus status, String errorCode, String errorMessage) {
        this.status = status;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.updatedTime = LocalDateTime.now();
    }
    
    /**
     * 设置回执时间
     */
    public void setCallbackTime(LocalDateTime callbackTime) {
        this.callbackTime = callbackTime;
        this.updatedTime = LocalDateTime.now();
    }
    
    /**
     * 是否为终态
     */
    public boolean isFinalStatus() {
        return status != null && status.isFinalStatus();
    }
}
