package com.xinfei.touch.domain.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 回执消息领域模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class ReceiptMessage {
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 触达渠道
     */
    private TouchChannel channel;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 策略ID
     */
    private Long strategyId;
    
    /**
     * 回执状态
     */
    private TouchStatus status;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 回执时间
     */
    private LocalDateTime receiptTime;
    
    /**
     * 是否需要业务回调
     */
    private Boolean needCallback;
    
    /**
     * 回调地址
     */
    private String callbackUrl;
    
    /**
     * 扩展参数
     */
    private Map<String, Object> extParams = new HashMap<>();
    
    /**
     * 原始回执数据
     */
    private String rawData;
    
    /**
     * 创建成功回执
     */
    public static ReceiptMessage success(String requestId, String batchNo, TouchChannel channel) {
        ReceiptMessage receipt = new ReceiptMessage();
        receipt.setRequestId(requestId);
        receipt.setBatchNo(batchNo);
        receipt.setChannel(channel);
        receipt.setStatus(TouchStatus.SUCCESS);
        receipt.setReceiptTime(LocalDateTime.now());
        receipt.setNeedCallback(false);
        return receipt;
    }
    
    /**
     * 创建失败回执
     */
    public static ReceiptMessage failed(String requestId, String batchNo, TouchChannel channel, 
                                       String errorCode, String errorMessage) {
        ReceiptMessage receipt = new ReceiptMessage();
        receipt.setRequestId(requestId);
        receipt.setBatchNo(batchNo);
        receipt.setChannel(channel);
        receipt.setStatus(TouchStatus.FAILED);
        receipt.setErrorCode(errorCode);
        receipt.setErrorMessage(errorMessage);
        receipt.setReceiptTime(LocalDateTime.now());
        receipt.setNeedCallback(false);
        return receipt;
    }
}
