package com.xinfei.touch.domain.service;

import com.xinfei.touch.domain.model.FlowControlRule;

import java.util.List;

/**
 * 频控检查器接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface FlowControlChecker {
    
    /**
     * 检查频控
     * 
     * @param key 频控键
     * @param limitTimes 限制次数
     * @param limitSeconds 限制时间（秒）
     * @return 是否通过检查
     */
    boolean check(String key, Integer limitTimes, Integer limitSeconds);
    
    /**
     * 获取分布式锁
     * 
     * @param lockKey 锁键
     * @param expireSeconds 过期时间（秒）
     * @return 是否获取成功
     */
    boolean acquireLock(String lockKey, int expireSeconds);
    
    /**
     * 释放分布式锁
     * 
     * @param lockKey 锁键
     */
    void releaseLock(String lockKey);
    
    /**
     * 批量过滤
     * 
     * @param userIds 用户ID列表
     * @param rule 流控规则
     * @return 通过流控的用户ID列表
     */
    List<Long> batchFilter(List<Long> userIds, FlowControlRule rule);
    
    /**
     * 获取当前计数
     * 
     * @param key 频控键
     * @return 当前计数
     */
    Long getCurrentCount(String key);
    
    /**
     * 重置计数
     * 
     * @param key 频控键
     */
    void resetCount(String key);
}
