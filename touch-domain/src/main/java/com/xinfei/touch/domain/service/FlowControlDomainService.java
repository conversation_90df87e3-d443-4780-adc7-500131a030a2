package com.xinfei.touch.domain.service;

import com.xinfei.touch.domain.model.FlowControlRule;
import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.repository.FlowControlRuleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 频控领域服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlowControlDomainService {
    
    private final FlowControlRuleRepository flowControlRuleRepository;
    private final FlowControlChecker flowControlChecker;
    
    /**
     * 检查事件级流控
     * 
     * @param eventType 事件类型
     * @param userId 用户ID
     * @return 是否通过流控检查
     */
    public boolean checkEventFlowControl(String eventType, Long userId) {
        log.debug("检查事件级流控: eventType={}, userId={}", eventType, userId);
        
        try {
            // 获取匹配的事件级流控规则
            List<FlowControlRule> rules = flowControlRuleRepository.findEventFlowControlRules(eventType);
            
            // 按优先级排序检查规则
            for (FlowControlRule rule : rules) {
                if (!checkSingleRule(rule, userId, null, null, eventType)) {
                    log.info("事件级流控拦截: eventType={}, userId={}, rule={}", eventType, userId, rule.getRuleName());
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("检查事件级流控异常: eventType={}, userId={}", eventType, userId, e);
            // 异常情况下放行，避免影响业务
            return true;
        }
    }
    
    /**
     * 检查触达级流控
     * 
     * @param channel 触达渠道
     * @param strategyId 策略ID
     * @param userId 用户ID
     * @param bizEventType 业务事件类型
     * @return 是否通过流控检查
     */
    public boolean checkTouchFlowControl(TouchChannel channel, Long strategyId, Long userId, String bizEventType) {
        log.debug("检查触达级流控: channel={}, strategyId={}, userId={}, bizEventType={}", 
                channel, strategyId, userId, bizEventType);
        
        try {
            // 获取匹配的触达级流控规则
            List<FlowControlRule> rules = flowControlRuleRepository.findTouchFlowControlRules(
                    channel, strategyId, bizEventType);
            
            // 按优先级排序检查规则
            for (FlowControlRule rule : rules) {
                if (!checkSingleRule(rule, userId, channel, strategyId, bizEventType)) {
                    log.info("触达级流控拦截: channel={}, strategyId={}, userId={}, rule={}", 
                            channel, strategyId, userId, rule.getRuleName());
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("检查触达级流控异常: channel={}, strategyId={}, userId={}", channel, strategyId, userId, e);
            // 异常情况下放行，避免影响业务
            return true;
        }
    }
    
    /**
     * 获取分布式锁
     * 
     * @param lockKey 锁键
     * @param expireSeconds 过期时间（秒）
     * @return 是否获取成功
     */
    public boolean acquireDistributedLock(String lockKey, int expireSeconds) {
        log.debug("获取分布式锁: lockKey={}, expireSeconds={}", lockKey, expireSeconds);
        
        try {
            return flowControlChecker.acquireLock(lockKey, expireSeconds);
        } catch (Exception e) {
            log.error("获取分布式锁异常: lockKey={}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 释放分布式锁
     * 
     * @param lockKey 锁键
     */
    public void releaseDistributedLock(String lockKey) {
        log.debug("释放分布式锁: lockKey={}", lockKey);
        
        try {
            flowControlChecker.releaseLock(lockKey);
        } catch (Exception e) {
            log.error("释放分布式锁异常: lockKey={}", lockKey, e);
        }
    }
    
    /**
     * 批量流控过滤
     * 
     * @param userIds 用户ID列表
     * @param rule 流控规则
     * @return 通过流控的用户ID列表
     */
    public List<Long> filterByFlowControl(List<Long> userIds, FlowControlRule rule) {
        log.debug("批量流控过滤: userCount={}, rule={}", userIds.size(), rule.getRuleName());
        
        return flowControlChecker.batchFilter(userIds, rule);
    }
    
    /**
     * 检查单个规则
     */
    private boolean checkSingleRule(FlowControlRule rule, Long userId, TouchChannel channel, 
                                   Long strategyId, String bizEventType) {
        // 检查规则是否匹配
        if (!rule.matches(channel, strategyId, bizEventType)) {
            return true;
        }
        
        // 构建流控键
        String flowControlKey = rule.buildFlowControlKey(userId);
        
        // 执行流控检查
        return flowControlChecker.check(flowControlKey, rule.getLimitTimes(), rule.getLimitSeconds());
    }
}
