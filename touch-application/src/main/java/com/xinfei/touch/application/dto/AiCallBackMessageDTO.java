package com.xinfei.touch.application.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * AI外呼回执DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class AiCallBackMessageDTO {
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 策略ID
     */
    private Long strategyId;
    
    /**
     * 回执状态：FILTER-被过滤，CALLED-已拨打，FAILURE-推送失败
     */
    private String status;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 回执时间
     */
    private LocalDateTime receiptTime;
    
    /**
     * 外呼结果
     */
    private String callResult;
    
    /**
     * 通话时长（秒）
     */
    private Integer callDuration;
    
    /**
     * 录音地址
     */
    private String recordUrl;
    
    /**
     * AI识别结果
     */
    private String aiResult;
    
    /**
     * 扩展信息
     */
    private String extInfo;
}
