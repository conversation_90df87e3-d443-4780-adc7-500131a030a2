package com.xinfei.touch.application.service;

import com.xinfei.touch.domain.model.ReceiptMessage;
import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.service.ReceiptDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 回执应用服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReceiptApplicationService {
    
    private final ReceiptDomainService receiptDomainService;
    private final TouchMonitorService touchMonitorService;
    

    
    /**
     * 处理短信回执列表（MQ消费）
     *
     * @param smsReportList 短信回执列表
     */
    public void processSmsReceipts(List<com.xinfei.touch.application.dto.SmsReportDTO> smsReportList) {
        try {
            log.info("批量处理短信回执: count={}", smsReportList.size());

            for (com.xinfei.touch.application.dto.SmsReportDTO smsReport : smsReportList) {
                try {
                    // 转换为标准回执消息
                    ReceiptMessage receiptMessage = convertSmsReport(smsReport);

                    // 领域服务处理
                    receiptDomainService.processReceipt(receiptMessage);

                    // 监控上报
                    reportReceiptMonitoring(receiptMessage);

                } catch (Exception e) {
                    log.error("处理单条短信回执失败: batchNo={}", smsReport.getBatchNo(), e);
                }
            }

            log.info("短信回执批量处理完成: count={}", smsReportList.size());

        } catch (Exception e) {
            log.error("批量处理短信回执失败", e);
        }
    }

    /**
     * 处理优惠券回执列表（MQ消费）
     *
     * @param couponCallbackList 优惠券回执列表
     */
    public void processCouponReceipts(List<com.xinfei.touch.application.dto.CouponCallbackDTO> couponCallbackList) {
        try {
            log.info("批量处理优惠券回执: count={}", couponCallbackList.size());

            for (com.xinfei.touch.application.dto.CouponCallbackDTO couponCallback : couponCallbackList) {
                try {
                    // 转换为标准回执消息
                    ReceiptMessage receiptMessage = convertCouponCallback(couponCallback);

                    // 领域服务处理
                    receiptDomainService.processReceipt(receiptMessage);

                    // 特殊业务回执处理（发送MQ消息）
                    processSpecialCouponCallback(couponCallback);

                    // 监控上报
                    reportReceiptMonitoring(receiptMessage);

                } catch (Exception e) {
                    log.error("处理单条优惠券回执失败: batchNo={}", couponCallback.getBatchNo(), e);
                }
            }

            log.info("优惠券回执批量处理完成: count={}", couponCallbackList.size());

        } catch (Exception e) {
            log.error("批量处理优惠券回执失败", e);
        }
    }

    /**
     * 处理AI外呼回执（MQ消费）
     *
     * @param aiCallBackMessage AI外呼回执消息
     */
    public void processAiCallbackReceipt(com.xinfei.touch.application.dto.AiCallBackMessageDTO aiCallBackMessage) {
        try {
            log.info("处理AI外呼回执: batchNo={}, status={}",
                    aiCallBackMessage.getBatchNo(), aiCallBackMessage.getStatus());

            // 转换为标准回执消息
            ReceiptMessage receiptMessage = convertAiCallback(aiCallBackMessage);

            // 领域服务处理
            receiptDomainService.processReceipt(receiptMessage);

            // 监控上报
            reportReceiptMonitoring(receiptMessage);

            log.info("AI外呼回执处理完成: batchNo={}", aiCallBackMessage.getBatchNo());

        } catch (Exception e) {
            log.error("处理AI外呼回执失败: batchNo={}", aiCallBackMessage.getBatchNo(), e);
        }
    }

    /**
     * 处理Push回执（直接方法调用）
     *
     * @param pushReportReq Push回执请求
     */
    public void processPushReceipt(Object pushReportReq) {
        try {
            log.info("处理Push回执: {}", pushReportReq);

            // 转换为标准回执消息
            ReceiptMessage receiptMessage = convertPushReport(pushReportReq);

            // 领域服务处理
            receiptDomainService.processReceipt(receiptMessage);

            // 监控上报
            reportReceiptMonitoring(receiptMessage);

            log.info("Push回执处理完成: requestId={}", receiptMessage.getRequestId());

        } catch (Exception e) {
            log.error("处理Push回执失败", e);
        }
    }
    

    
    /**
     * 监控上报
     */
    private void reportReceiptMonitoring(ReceiptMessage receiptMessage) {
        try {
            touchMonitorService.reportReceipt(receiptMessage);
        } catch (Exception e) {
            log.error("回执监控上报失败: requestId={}", receiptMessage.getRequestId(), e);
        }
    }

    /**
     * 转换短信回执
     */
    private ReceiptMessage convertSmsReport(com.xinfei.touch.application.dto.SmsReportDTO smsReport) {
        ReceiptMessage receiptMessage = new ReceiptMessage();
        receiptMessage.setBatchNo(smsReport.getBatchNo());
        receiptMessage.setChannel(TouchChannel.SMS);
        receiptMessage.setUserId(smsReport.getUserId());
        receiptMessage.setStrategyId(smsReport.getStrategyId());
        receiptMessage.setReceiptTime(smsReport.getReceiptTime() != null ? smsReport.getReceiptTime() : LocalDateTime.now());
        receiptMessage.setErrorCode(smsReport.getErrorCode());
        receiptMessage.setErrorMessage(smsReport.getErrorMessage());

        // 状态转换
        if ("delivered".equals(smsReport.getStatus()) || "success".equals(smsReport.getStatus())) {
            receiptMessage.setStatus(com.xinfei.touch.domain.model.TouchStatus.SUCCESS);
        } else if ("failed".equals(smsReport.getStatus())) {
            receiptMessage.setStatus(com.xinfei.touch.domain.model.TouchStatus.FAILED);
        } else {
            receiptMessage.setStatus(com.xinfei.touch.domain.model.TouchStatus.PROCESSING);
        }

        // 扩展信息
        receiptMessage.getExtParams().put("mobile", smsReport.getMobile());
        receiptMessage.getExtParams().put("content", smsReport.getContent());
        receiptMessage.getExtParams().put("supplierId", smsReport.getSupplierId());

        return receiptMessage;
    }

    /**
     * 转换优惠券回执
     */
    private ReceiptMessage convertCouponCallback(com.xinfei.touch.application.dto.CouponCallbackDTO couponCallback) {
        ReceiptMessage receiptMessage = new ReceiptMessage();
        receiptMessage.setBatchNo(couponCallback.getBatchNo());
        receiptMessage.setChannel(TouchChannel.COUPON);
        receiptMessage.setUserId(couponCallback.getUserId());
        receiptMessage.setStrategyId(couponCallback.getStrategyId());
        receiptMessage.setReceiptTime(couponCallback.getReceiptTime() != null ? couponCallback.getReceiptTime() : LocalDateTime.now());
        receiptMessage.setErrorCode(couponCallback.getErrorCode());
        receiptMessage.setErrorMessage(couponCallback.getErrorMessage());

        // 状态转换
        if ("successed".equals(couponCallback.getStatus())) {
            receiptMessage.setStatus(com.xinfei.touch.domain.model.TouchStatus.SUCCESS);
        } else if ("failed".equals(couponCallback.getStatus())) {
            receiptMessage.setStatus(com.xinfei.touch.domain.model.TouchStatus.FAILED);
        } else if ("used".equals(couponCallback.getStatus())) {
            receiptMessage.setStatus(com.xinfei.touch.domain.model.TouchStatus.SUCCESS);
        } else {
            receiptMessage.setStatus(com.xinfei.touch.domain.model.TouchStatus.PROCESSING);
        }

        // 扩展信息
        receiptMessage.getExtParams().put("usedStatus", couponCallback.getUsedStatus());
        receiptMessage.getExtParams().put("couponType", couponCallback.getCouponType());
        receiptMessage.getExtParams().put("bizType", couponCallback.getBizType());
        receiptMessage.getExtParams().put("couponId", couponCallback.getCouponId());
        receiptMessage.getExtParams().put("couponAmount", couponCallback.getCouponAmount());

        return receiptMessage;
    }

    /**
     * 转换AI外呼回执
     */
    private ReceiptMessage convertAiCallback(com.xinfei.touch.application.dto.AiCallBackMessageDTO aiCallback) {
        ReceiptMessage receiptMessage = new ReceiptMessage();
        receiptMessage.setBatchNo(aiCallback.getBatchNo());
        receiptMessage.setChannel(TouchChannel.VOICE);
        receiptMessage.setUserId(aiCallback.getUserId());
        receiptMessage.setStrategyId(aiCallback.getStrategyId());
        receiptMessage.setReceiptTime(aiCallback.getReceiptTime() != null ? aiCallback.getReceiptTime() : LocalDateTime.now());
        receiptMessage.setErrorCode(aiCallback.getErrorCode());
        receiptMessage.setErrorMessage(aiCallback.getErrorMessage());

        // 状态转换
        if ("CALLED".equals(aiCallback.getStatus())) {
            receiptMessage.setStatus(com.xinfei.touch.domain.model.TouchStatus.SUCCESS);
        } else if ("FAILURE".equals(aiCallback.getStatus()) || "FILTER".equals(aiCallback.getStatus())) {
            receiptMessage.setStatus(com.xinfei.touch.domain.model.TouchStatus.FAILED);
        } else {
            receiptMessage.setStatus(com.xinfei.touch.domain.model.TouchStatus.PROCESSING);
        }

        // 扩展信息
        receiptMessage.getExtParams().put("callResult", aiCallback.getCallResult());
        receiptMessage.getExtParams().put("callDuration", aiCallback.getCallDuration());
        receiptMessage.getExtParams().put("recordUrl", aiCallback.getRecordUrl());
        receiptMessage.getExtParams().put("aiResult", aiCallback.getAiResult());

        return receiptMessage;
    }

    /**
     * 转换Push回执
     */
    private ReceiptMessage convertPushReport(Object pushReportReq) {
        // 这里需要根据实际的Push回执对象结构进行转换
        ReceiptMessage receiptMessage = new ReceiptMessage();
        receiptMessage.setChannel(TouchChannel.PUSH);
        receiptMessage.setReceiptTime(LocalDateTime.now());
        receiptMessage.setStatus(com.xinfei.touch.domain.model.TouchStatus.SUCCESS);

        // TODO: 根据实际Push回执结构完善转换逻辑

        return receiptMessage;
    }

    /**
     * 处理特殊优惠券业务回执
     */
    private void processSpecialCouponCallback(com.xinfei.touch.application.dto.CouponCallbackDTO couponCallback) {
        try {
            Integer couponType = couponCallback.getCouponType();
            if (couponType == null) {
                return;
            }

            // 生活权益回执
            if (couponType == 4) {
                sendLifeRightsCallback(couponCallback);
            }
            // X天免息回执
            else if (couponType == 5) {
                sendXDayInterestFreeCallback(couponCallback);
            }

        } catch (Exception e) {
            log.error("处理特殊优惠券业务回执失败: batchNo={}, couponType={}",
                    couponCallback.getBatchNo(), couponCallback.getCouponType(), e);
        }
    }

    /**
     * 发送生活权益回执
     */
    private void sendLifeRightsCallback(com.xinfei.touch.application.dto.CouponCallbackDTO couponCallback) {
        // TODO: 发送到 tp_xyf_cdp_notify:tg_liferights
        log.info("发送生活权益回执: batchNo={}", couponCallback.getBatchNo());
    }

    /**
     * 发送X天免息回执
     */
    private void sendXDayInterestFreeCallback(com.xinfei.touch.application.dto.CouponCallbackDTO couponCallback) {
        // TODO: 发送到 tp_xyf_cdp_notify:tg_xDayInterestFree
        log.info("发送X天免息回执: batchNo={}", couponCallback.getBatchNo());
    }
}
