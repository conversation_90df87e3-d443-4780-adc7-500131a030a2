package com.xinfei.touch.application.service;

import com.xinfei.touch.application.dto.PageResult;
import com.xinfei.touch.application.dto.TouchRecordDto;
import com.xinfei.touch.application.dto.TouchRequest;
import com.xinfei.touch.application.dto.TouchResponse;
import com.xinfei.touch.application.command.TouchCommand;
import com.xinfei.touch.application.assembler.TouchAssembler;
import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.model.TouchStatus;
import com.xinfei.touch.domain.service.FlowControlDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 触达应用服务 - 统一入口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TouchApplicationService {
    
    private final TouchRequestProcessor touchRequestProcessor;
    private final TouchQueryService touchQueryService;
    private final FlowControlDomainService flowControlDomainService;
    private final TouchAssembler touchAssembler;
    
    /**
     * 统一触达处理（支持T0实时触达和离线触达）
     *
     * @param request 触达请求
     * @return 触达响应
     */
    public TouchResponse processTouch(TouchRequest request) {
        log.info("处理触达请求: requestId={}, touchType={}, channel={}, userId={}",
                request.getRequestId(), request.getTouchType(), request.getChannel(), request.getUserId());
        
        try {
            // 1. 请求验证
            validateTouchRequest(request);
            
            // 2. 转换为领域命令
            TouchCommand command = touchAssembler.toCommand(request);
            
            // 3. 执行触达处理
            TouchResponse response = touchRequestProcessor.process(command);
            
            log.info("触达处理完成: requestId={}, touchType={}, status={}",
                    request.getRequestId(), request.getTouchType(), response.getStatus());
            
            return response;
            
        } catch (Exception e) {
            log.error("触达处理失败: requestId={}, touchType={}", request.getRequestId(), request.getTouchType(), e);
            return TouchResponse.failed(request.getRequestId(), "PROCESS_FAILED", e.getMessage());
        }
    }
    
    /**
     * 批量触达处理
     * 
     * @param requests 触达请求列表
     * @return 触达响应列表
     */
    public List<TouchResponse> processBatchTouch(List<TouchRequest> requests) {
        log.info("处理批量触达请求: size={}", requests.size());
        
        try {
            // 1. 批量验证
            requests.forEach(this::validateTouchRequest);
            
            // 2. 转换为领域命令
            List<TouchCommand> commands = requests.stream()
                    .map(touchAssembler::toCommand)
                    .collect(Collectors.toList());
            
            // 3. 并行处理
            List<CompletableFuture<TouchResponse>> futures = commands.stream()
                    .map(command -> CompletableFuture.supplyAsync(() -> touchRequestProcessor.process(command)))
                    .collect(Collectors.toList());
            
            // 4. 等待所有处理完成
            List<TouchResponse> responses = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());
            
            log.info("批量触达处理完成: size={}", responses.size());
            
            return responses;
            
        } catch (Exception e) {
            log.error("批量触达处理失败", e);
            throw new RuntimeException("批量触达处理失败", e);
        }
    }
    
    /**
     * 查询触达状态
     * 
     * @param requestId 请求ID
     * @return 触达状态
     */
    public TouchStatus getTouchStatus(String requestId) {
        log.debug("查询触达状态: requestId={}", requestId);
        
        return touchQueryService.getTouchStatus(requestId);
    }
    
    /**
     * 查询触达记录
     * 
     * @param userId 用户ID
     * @param strategyId 策略ID
     * @param channel 触达渠道
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    public PageResult<TouchRecordDto> getTouchRecords(Long userId, Long strategyId, TouchChannel channel, 
                                                     int pageNum, int pageSize) {
        log.debug("查询触达记录: userId={}, strategyId={}, channel={}, page={}/{}", 
                userId, strategyId, channel, pageNum, pageSize);
        
        return touchQueryService.getTouchRecords(userId, strategyId, channel, pageNum, pageSize);
    }
    
    /**
     * 验证触达请求
     */
    private void validateTouchRequest(TouchRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("触达请求不能为空");
        }
        
        if (request.getRequestId() == null || request.getRequestId().trim().isEmpty()) {
            throw new IllegalArgumentException("请求ID不能为空");
        }
        
        if (request.getTouchType() == null) {
            throw new IllegalArgumentException("触达类型不能为空");
        }
        
        if (request.getChannel() == null) {
            throw new IllegalArgumentException("触达渠道不能为空");
        }
        
        if (request.getStrategyId() == null) {
            throw new IllegalArgumentException("策略ID不能为空");
        }
        
        if (request.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        // 设置默认时间戳
        if (request.getTimestamp() == null) {
            request.setTimestamp(System.currentTimeMillis());
        }
    }
}
