package com.xinfei.touch.api.dto;

import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.model.TouchType;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 触达请求统一模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class TouchRequest {
    
    /**
     * 请求唯一标识
     */
    @NotBlank(message = "请求ID不能为空")
    private String requestId;
    
    /**
     * 触达类型：REALTIME_NORMAL, REALTIME_ENGINE, OFFLINE_NORMAL, OFFLINE_ENGINE
     */
    @NotNull(message = "触达类型不能为空")
    private TouchType touchType;
    
    /**
     * 触达渠道：SMS, VOICE, PUSH, COUPON, AI_CALL等
     */
    @NotNull(message = "触达渠道不能为空")
    private TouchChannel channel;
    
    /**
     * 策略ID
     */
    @NotNull(message = "策略ID不能为空")
    private Long strategyId;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 业务事件类型
     */
    private String bizEventType;
    
    /**
     * 模板参数
     */
    private Map<String, Object> templateParams;
    
    /**
     * 触达配置
     */
    private TouchConfigDto touchConfig;
    
    /**
     * 请求时间戳
     */
    private Long timestamp;
    
    /**
     * 扩展参数
     */
    private Map<String, Object> extParams;

    // ========== 原DispatchDto中的重要字段 ==========

    /**
     * 策略执行ID
     */
    private String strategyExecId;

    /**
     * 明细表序号
     */
    private String detailTableNo;

    /**
     * 策略分组ID
     */
    private Long strategyGroupId;

    /**
     * 策略分组名称
     */
    private String strategyGroupName;

    /**
     * 策略渠道ID
     */
    private Long strategyChannelId;

    /**
     * 渠道模板ID
     */
    private String templateId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 触达类型（NOTIFY为通知，不流控）
     */
    private String dispatchType;

    /**
     * 短信签名
     */
    private String signatureKey;

    /**
     * 优惠券活动ID
     */
    private String activityId;

    /**
     * 类型ID
     */
    private String nameTypeId;

    /**
     * 业务线类型
     */
    private String bizType;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 应用标识
     */
    private String app;

    /**
     * 内部应用标识
     */
    private String innerApp;

    /**
     * 设备ID（Push渠道使用）
     */
    private String deviceId;

    /**
     * 手机号（短信、电销渠道使用）
     */
    private String mobile;

    /**
     * 追踪ID
     */
    private String traceId;
}
