/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.appbanner;

import cn.hutool.core.util.IdUtil;
import com.xftech.cdp.api.dto.req.AppBannerTemplateReq;
import com.xftech.cdp.api.dto.resp.AppBannerTemplateResp;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ AppBannerService, v 0.1 2024/4/30 14:11 benlin.wang Exp $
 */

@Slf4j
@Service
public class AppBannerClient {
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private HttpClientUtil httpClientUtil;

    public List<AppBannerTemplateResp> requestAppBannerList(AppBannerTemplateReq appBannerListRequest) {
        appBannerListRequest.setUtm_source("cdp");

        Map<String, String> headers = new HashMap<>();
        headers.put("trace-id", IdUtil.fastSimpleUUID());

        List<AppBannerTemplateResp> respList = new ArrayList<>();
        String url = appConfigService.getAppBannerSourceURL() + "/rpc/popup/list";

        String param = JsonUtil.toJson(appBannerListRequest);
        String resp = httpClientUtil.postForJson(url, param, headers);
        Map<String, Object> respObj = JsonUtil.parse(resp, Map.class);
        log.info("AppBannerClient url={} param={} headers={} resp={}", url, param, headers, resp);
        if (respObj != null && respObj.containsKey("suc") && (boolean) (respObj.get("suc"))) {
            if (respObj.containsKey("data")) {
                Map<String, Object> dataMap = (Map) (respObj.get("data"));
                if (dataMap.containsKey("list")) {
                    List<Map<String, Object>> listObject = (List<Map<String, Object>>) (dataMap.get("list"));
                    for (Map<String, Object> respMap : listObject) {
                        AppBannerTemplateResp appResp = new AppBannerTemplateResp();
                        respList.add(appResp);
                        appResp.setId(respMap.get("id").toString());
                        appResp.setBusiness(respMap.get("biz_type").toString());
                        appResp.setStatus(respMap.get("status").toString());
                        appResp.setApp(respMap.get("app").toString());
                        appResp.setName(respMap.get("resource_name").toString());
                        appResp.setStyle(respMap.get("bind_logic").toString());
                    }
                }
            }
        }
        return respList;
    }
}