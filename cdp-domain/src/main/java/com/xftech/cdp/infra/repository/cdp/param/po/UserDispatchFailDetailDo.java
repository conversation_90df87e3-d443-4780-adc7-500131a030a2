package com.xftech.cdp.infra.repository.cdp.param.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 模板参数匹配失败用户明细表
 *
 * @TableName template_param_fail_user_detail
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserDispatchFailDetailDo extends Do {
    /**
     * APP用户ID
     */
    private Long appUserId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * app
     */
    private String app;

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 营销渠道，0: 不营销，1:短信，2:电销，3:优惠券
     */
    private Integer marketChannel;

    /**
     * 模板类型 1短信 2电销 3优惠券
     */
    private Integer templateType;

    /**
     * 模板编号
     */
    private String templateNo;

    /**
     * 状态 0-失败 1-成功
     */
    private Integer status;

    /**
     * 触达时间
     */
    private LocalDateTime dispatchTime;

    /**
     * 识别原因
     */
    private String failReason;

    private String groupName;

    private String bizEventType;

    private String dispatchType;

    private static final long serialVersionUID = 1L;
}