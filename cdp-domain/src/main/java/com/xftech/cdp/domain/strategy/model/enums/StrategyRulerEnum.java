package com.xftech.cdp.domain.strategy.model.enums;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@AllArgsConstructor
@Getter
public enum StrategyRulerEnum {

    /**
     * 单次
     */
    ONCE(0, "单次",StrategyRulerEnum::getOffline),

    /**
     * 例行
     */
    CYCLE(1, "例行",StrategyRulerEnum::getOffline),

    /**
     * 事件
     */
    EVENT(2, "事件",StrategyRulerEnum::getRealtime),

    /**
     * 按循环周期天数平均分组
     */
    CYCLE_DAY(3, "每日循环周期",StrategyRulerEnum::getOffline);



    @FunctionalInterface
    public interface StrategySupplier {
        String getStrategyType();
    }

    private final int code;

    private final String description;

    private final StrategySupplier strategySupplier;

    public static StrategyRulerEnum getInstance(Integer code) {
        for (StrategyRulerEnum rulerEnum : StrategyRulerEnum.values()) {
            if (Objects.equals(rulerEnum.getCode(), code)) {
                return rulerEnum;
            }
        }
        throw new StrategyException(String.format("不存在改类型的发送规则：%s", code));
    }

    public static List<Integer> getCycleCodes() {
        return getCycleCodeEnums().stream().map(x -> x.code)
                .collect(Collectors.toList());
    }
    public static List<StrategyRulerEnum> getCycleCodeEnums() {
        return Stream.of(CYCLE, CYCLE_DAY)
                .collect(Collectors.toList());
    }

    public String getStrategyType() {
        return strategySupplier.getStrategyType();
    }
    private static String getRealtime() {
        return "realtime";
    }

    private static String getOffline() {
        return "offline";
    }
}
