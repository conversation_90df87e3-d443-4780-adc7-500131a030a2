package com.xftech.cdp.domain.strategy.model.enums;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.infra.constant.StrategyDispatchConstants;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/22
 */
@Getter
@AllArgsConstructor
public enum StrategyMarketChannelEnum {
    BLANK(-2, "", "空"),
    FILTER(-1, "", "被过滤组"),
    NONE(0, StrategyDispatchConstants.NONE_SERVICE, "不营销"),
    SMS(1, StrategyDispatchConstants.SMS_SERVICE, "短信"),
    VOICE(2, StrategyDispatchConstants.TELE_SERVICE, "电销"),
    VOICE_NEW(4, StrategyDispatchConstants.NEW_TELE_SERVICE, "新电销"),
    SALE_TICKET(3, StrategyDispatchConstants.COUPON_SERVICE, "优惠券"),
    PUSH(5, StrategyDispatchConstants.PUSH_SERVICE, "push"),
    AI_PRONTO(6, StrategyDispatchConstants.AI_PRONTO_SERVICE, "AI-即时触达"),

    // 200
    INCREASE_AMOUNT(200, StrategyDispatchConstants.INCREASE_AMOUNT_SERVICE, "提额"),
    X_DAY_INTEREST_FREE(201, StrategyDispatchConstants.X_DAY_INTEREST_FREE_SERVICE, "X天还款免息"),
    LIFE_RIGHTS(202, StrategyDispatchConstants.LIFE_RIGHTS, "生活权益"),
    API_OPEN_AMOUNT(203, StrategyDispatchConstants.API_OPEN_AMOUNT_SERVICE, "api放开额度"),
    REMAINING_SUM_CHARGING(204, StrategyDispatchConstants.REMAINING_SUM_RECHARGING, "用户余额返现"),


    APP_BANNER(100, "", "app资源位"),
    LOAN_OVERLOAD(101, "", "贷超"),
    API_HOLD(102, "", "api hold单"),

    RE_DECISION(999, "", "业务引擎-延迟决策"),

    ;
    private final int code;

    private final String service;

    private final String description;

    public static StrategyMarketChannelEnum getInstance(Integer code) {
        for (StrategyMarketChannelEnum strategyMarketChannelEnum : StrategyMarketChannelEnum.values()) {
            if (Objects.equals(strategyMarketChannelEnum.getCode(), code)) {
                return strategyMarketChannelEnum;
            }
        }
        throw new StrategyException(String.format("渠道类型异常：%s", code));
    }

    public static StrategyMarketChannelEnum getInstance(String type) {
        if (StringUtils.equalsIgnoreCase("sms", type)) {
            return SMS;
        } else if (StringUtils.equalsIgnoreCase("voice", type)) {
            return VOICE;
        } else if (StringUtils.equalsIgnoreCase("coupon", type)) {
            return SALE_TICKET;
        } else if (StringUtils.equalsIgnoreCase("none", type)) {
            return NONE;
        } else if (StringUtils.equalsIgnoreCase("voiceNew", type)) {
            return VOICE_NEW;
        } else if (StringUtils.equalsIgnoreCase("push", type)) {
            return PUSH;
        } else if (StringUtils.equalsIgnoreCase("increaseAmt", type)) {
            return INCREASE_AMOUNT;
        } else if (StringUtils.equalsIgnoreCase("appBanner", type)) {
            return APP_BANNER;
        } else if (StringUtils.equalsIgnoreCase("xDayInterestFree", type)) {
            return X_DAY_INTEREST_FREE;
        } else if (StringUtils.equalsIgnoreCase("ai", type)) {
            return AI_PRONTO;
        }  else if (StringUtils.equalsIgnoreCase("recharge", type)) {
            return REMAINING_SUM_CHARGING;
        }   else if (StringUtils.equalsIgnoreCase("reInput", type)) {
            return RE_DECISION;
        }else {
            return NONE;
        }
    }

    public static List<Integer> getIgnoreCodes(){
        return Arrays.asList(FILTER.code, NONE.code, BLANK.code, APP_BANNER.code);
    }

    public static List<Integer> getIgnoreMarketingCodes(){
        return Arrays.asList(FILTER.code, BLANK.code, APP_BANNER.code);
    }

    public static List<Integer> hasTemplateId() {
        return Arrays.asList(FILTER.getCode(), SMS.getCode(), VOICE.getCode(), SALE_TICKET.getCode());
    }

    public static List<Integer> getFlcCodes() {
        return Arrays.asList(SMS.getCode(), VOICE.getCode(), SALE_TICKET.getCode(), VOICE_NEW.getCode(),
                INCREASE_AMOUNT.getCode(),PUSH.getCode(), AI_PRONTO.getCode());
    }

    public static List<Integer> getNoFlcCodes() {
        if(WhitelistSwitchUtil.boolSwitchByApollo("noFlcCodesRemovePushEnable")) { // TODO 后续可去除开关逻辑
            return Arrays.asList(APP_BANNER.getCode());
        } else {
            return Arrays.asList(PUSH.getCode(), APP_BANNER.getCode());
        }
    }
}
