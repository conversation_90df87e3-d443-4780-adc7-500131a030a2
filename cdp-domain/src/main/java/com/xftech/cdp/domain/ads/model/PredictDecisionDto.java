package com.xftech.cdp.domain.ads.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Data
public class PredictDecisionDto {
    private Integer decision_code;
    private String decision_msg;
    private DecisionData decision_data;

    @JsonIgnore
    @JSONField(serialize = false)
    public boolean isSucced() {
        return Objects.equals(200, decision_code);
    }

    @JsonIgnore
    @JSONField(serialize = false)
    public boolean isDelay() {
        if (decision_data != null &&
                decision_data.delay != null) {
            return decision_data.delay.getSeconds() > 0;
        }
        return false;
    }

    public List<DecisionData.Action> getActions() {
        if (decision_data != null &&
                !CollectionUtils.isEmpty(decision_data.getActions())) {
            return decision_data.actions;
        }
        return new ArrayList<>(0);
    }

    @JsonIgnore
    @JSONField(serialize = false)
    public long getDelaySeconds() {
        if (decision_data != null &&
                decision_data.delay != null) {
            return decision_data.delay.getSeconds();
        }
        return 0L;
    }

    @Data
    public static class DecisionData {
        private Delay delay;
        private List<Action> actions;

        @Data
        public static class Action {
            private String group_id;
            private String group_source;
            private List<Dispatch> dispatch;

            @Data
            public static class Dispatch {
                private Integer order;
                private String type;
                private String dispatchTime; // "2023-11-23 9:00:00"
                private Map detail_info;
            }

            @JsonIgnore
            @JSONField(serialize = false)
            public List<DecisionData.Action.Dispatch> getOrderedDispatch() {
                if (!CollectionUtils.isEmpty(dispatch)) {
                    return dispatch.stream()
                            .sorted(Comparator.comparing(DecisionData.Action.Dispatch::getOrder))
                            .collect(Collectors.toList());
                }
                return new ArrayList<>(0);
            }
        }
        @Data
        public static class Delay {
            private Long seconds;
        }
    }

    @Data
    public static class DetailCouponDto {
        private Long activity_id;
        private String batch_num;
    }

    public Boolean ifMarket() {
        for (DecisionData.Action action : getActions()) {
            for (DecisionData.Action.Dispatch orderedDispatch : action.getOrderedDispatch()) {
                if (!StrategyMarketChannelEnum.NONE.equals(StrategyMarketChannelEnum.getInstance(orderedDispatch.getType())) &&
                        !StrategyMarketChannelEnum.FILTER.equals(StrategyMarketChannelEnum.getInstance(orderedDispatch.getType()))
                ) {
                    return true;
                }
            }
        }
        return false;
    }
}
