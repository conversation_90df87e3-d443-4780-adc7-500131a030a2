package com.xftech.cdp.domain.strategy.service.dispatch.event.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

import com.github.benmanes.caffeine.cache.Cache;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.distribute.crowd.service.CrowdInfoService;
import com.xftech.cdp.domain.cache.CacheCrowdPackService;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.service.CrowdLabelService;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.flowctrl.model.dto.FlowCtrlDto;
import com.xftech.cdp.domain.flowctrl.service.FlowCtrlCoreService;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyEventCheckContext;
import com.xftech.cdp.domain.strategy.model.dto.AiProntoChannelDto;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtDto;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtParamDto;
import com.xftech.cdp.domain.strategy.model.dto.UserDispatchFailDetailDto;
import com.xftech.cdp.domain.strategy.model.enums.StrategyGroupTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.model.enums.UserDispatchDetailStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.label.ValueTypeEnum;
import com.xftech.cdp.domain.strategy.repository.EventPushBatchRepository;
import com.xftech.cdp.domain.strategy.repository.UserDispatchDetailRepository;
import com.xftech.cdp.domain.strategy.service.EventMetaDataService;
import com.xftech.cdp.domain.strategy.service.StrategyGroupService;
import com.xftech.cdp.domain.strategy.service.UserDispatchFailDetailService;
import com.xftech.cdp.domain.strategy.service.UserSendCounterService;
import com.xftech.cdp.infra.aviator.enums.DataTimeFormat;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.client.ads.config.AdsConfig;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.utils.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.proxy.Tracer;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;

import static com.xftech.cdp.infra.rocketmq.EventEnum.*;

/**
 * @<NAME_EMAIL>
 */
@Slf4j
@Getter
public abstract class AbstractStrategyEventDispatchService {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private SerialNumberUtil serialNumberUtil;
    @Autowired
    private CrowdPackService crowdPackService;
    @Autowired
    private CrowdDetailRepository crowdDetailRepository;
    @Autowired
    private CacheCrowdPackService cacheCrowdPackService;
    @Autowired
    private StrategyGroupService strategyGroupService;
    @Autowired
    private FlowCtrlCoreService flowCtrlCoreService;
    @Autowired
    private EventMetaDataService eventMetaDataService;
    @Autowired
    private UserDispatchFailDetailService userDispatchFailDetailService;
    @Autowired
    private UserSendCounterService userSendSucceedCounterService;
    @Autowired
    private EventPushBatchRepository eventPushBatchRepository;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private RandomNumService randomNumService;
    @Autowired
    private CrowdLabelService crowdLabelService;
    @Autowired
    private AdsConfig adsConfig;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private CrowdInfoService crowdInfoService;

    /**
     * 消息体转实时策略消息体
     *
     * @param messageId         消息ID
     * @param bizEventMessageVO 消息体
     * @return 实时策略消息体
     */
    protected BizEventVO toBizEventVO(String messageId, BizEventMessageVO bizEventMessageVO) {
        BizEventVO bizEventVO = new BizEventVO();
        bizEventVO.setMessageId(messageId);
        bizEventVO.setBizEventType(bizEventMessageVO.getBizEventType());
        bizEventVO.setAppUserId(bizEventMessageVO.getCreditUserId());
        bizEventVO.setExt(bizEventMessageVO.getExt());
        if (StringUtils.equalsAny(bizEventMessageVO.getBizEventType(), "Login1IncreaseCreditLimit", "Start1IncreaseCreditLimit")) {
            bizEventVO.setAppUserId(bizEventMessageVO.getAppUserId());
        }
        bizEventVO.setMobile(bizEventMessageVO.getMobile());
        bizEventVO.setApp(bizEventMessageVO.getApp());
        bizEventVO.setOs(bizEventMessageVO.getOs());
        bizEventVO.setInnerApp(bizEventMessageVO.getInnerApp());
        bizEventVO.setUtmSource(bizEventMessageVO.getUtmSource());
        bizEventVO.setEngineCallerCount(1L);
        bizEventVO.setCreditUserId(bizEventMessageVO.getCreditUserId());
        bizEventVO.setDeviceId(bizEventMessageVO.getDeviceId());
        bizEventVO.setIp(bizEventMessageVO.getIp());
        if (bizEventMessageVO.getTriggerDatetime() != null) {
            bizEventVO.setTriggerDatetime(LocalDateTime.parse(bizEventMessageVO.getTriggerDatetime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            bizEventVO.setTriggerDateTimeHms(Integer.parseInt(DateTimeUtil.formatDateToStr(DateUtil.convert(bizEventMessageVO.getTriggerDatetime()), DataTimeFormat.DataTimeFormat26.getFormat()).replace(":", "")));
        }
        if (bizEventMessageVO.getExtrData() != null) {
            if (bizEventMessageVO.getExtrData().getAmount() != null) {
                bizEventVO.setAmount(bizEventMessageVO.getExtrData().getAmount());
            }
            if (bizEventMessageVO.getExtrData().getAdjustChange() != null) {
                bizEventVO.setAdjustChange(bizEventMessageVO.getExtrData().getAdjustChange());
            }
            if (bizEventMessageVO.getExtrData().getAdjustAmount() != null) {
                bizEventVO.setAdjustAmount(bizEventMessageVO.getExtrData().getAdjustAmount());
            }
            if (bizEventMessageVO.getExtrData().getCreatedTime() != null) {
                bizEventVO.setRegisterTime(LocalDateTime.parse(bizEventMessageVO.getExtrData().getCreatedTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if (bizEventMessageVO.getExtrData().getCurrentUtmSource() != null) {
                bizEventVO.setCurrentUtmSource(bizEventMessageVO.getExtrData().getCurrentUtmSource());
            }
            if (bizEventMessageVO.getExtrData().getSourceType() != null) {
                bizEventVO.setSourceType(bizEventMessageVO.getExtrData().getSourceType());
            }
            if (bizEventMessageVO.getExtrData().getOrderNumber() != null) {
                bizEventVO.setOrderNumber(bizEventMessageVO.getExtrData().getOrderNumber());
            }
            if (bizEventMessageVO.getExtrData().getRepayType() != null) {
                bizEventVO.setRepayType(bizEventMessageVO.getExtrData().getRepayType());
            }
            if (StringUtils.equalsIgnoreCase("LoanFinalFailed", bizEventMessageVO.getBizEventType())) {
                // 失败原因大类 风险失败=risk_failed;资金失败=fund_failed;其他原因=other
                bizEventVO.setLoanFailReason("other");
                Map<String, Set<String>> reasonCodesMap = JSON.parseObject(ApolloUtil.getAppProperty("loanFinalFailed.reason.codes", "{}"), new TypeReference<Map<String, Set<String>>>(){});
                reasonCodesMap.forEach((k, v) -> {
                    if (v.contains(bizEventMessageVO.getExtrData().getFail_reason())) {
                        bizEventVO.setLoanFailReason(k);
                    }
                });
                bizEventVO.setLoanFailReasonDetail(bizEventMessageVO.getExtrData().getFail_reason_detail());
            }

            if (StringUtils.equalsIgnoreCase("RiskActivation", bizEventMessageVO.getBizEventType())) {
                if (bizEventMessageVO.getExtrData().getAmount() != null) {
                    bizEventVO.setAmount(bizEventMessageVO.getExtrData().getAmount().divide(new BigDecimal("100")));
                }
                bizEventVO.setActivationStatus(bizEventMessageVO.getExtrData()
                        .getActivation_status());
            }
            if (StringUtils.equalsIgnoreCase("RiskTempCredit", bizEventMessageVO.getBizEventType())) {
                bizEventVO.setAmount(bizEventMessageVO.getExtrData().getAmount());
                bizEventVO.setAdjustAmount(bizEventMessageVO.getExtrData().getAdjustAmount());
                bizEventVO.setTemporaryQuotaEffectiveTime(bizEventMessageVO.getExtrData().getStart_time());
                bizEventVO.setTemporaryQuotaInvalidTime(bizEventMessageVO.getExtrData().getEnd_time());
            }
            if (StringUtils.equalsIgnoreCase("LoanSuccess", bizEventMessageVO.getBizEventType())) {
                //分转元
                bizEventVO.setAmount(bizEventMessageVO.getExtrData().getAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));

                bizEventVO.setFirst_loan_suc_amt(bizEventMessageVO.getExtrData().getAmount());
                bizEventVO.setFirstLoan(Objects.equals(bizEventMessageVO.getExtrData().getIs_first_loan(), Boolean.TRUE) ? 1 : 0);
                bizEventVO.setFundSourceType(bizEventMessageVO.getExtrData().getFund_source_type());
            }
            if (StringUtils.equalsIgnoreCase("LendTradeOrder", bizEventMessageVO.getBizEventType())) { // 放款事件(新)
                bizEventVO.setAmount(bizEventMessageVO.getExtrData().getAmount());
                bizEventVO.setFirstLoan(Objects.equals(bizEventMessageVO.getExtrData().getIs_first_loan(), Boolean.TRUE) ? 1 : 0);
                bizEventVO.setFundSourceType(bizEventMessageVO.getExtrData().getFund_source_type());
                bizEventVO.setCnlPdCode(bizEventMessageVO.getExtrData().getCnlPdCode());
                bizEventVO.setStatus(bizEventMessageVO.getExtrData().getStatus());
                // 失败原因大类 风险失败=risk_failed;资金失败=fund_failed;其他原因=other
                bizEventVO.setFailedReason("other");
                Map<String, Set<String>> reasonCodesMap = JSON.parseObject(ApolloUtil.getAppProperty("loanFinalFailed.reason.codes", "{}"), new TypeReference<Map<String, Set<String>>>(){});
                reasonCodesMap.forEach((k, v) -> {
                    if (v.contains(bizEventMessageVO.getExtrData().getFailedCode())) {
                        bizEventVO.setFailedReason(k);
                    }
                });
                if (StringUtils.isNotBlank(bizEventMessageVO.getExtrData().getCreateTime())) {
                    bizEventVO.setCreateTimeHms(Integer.parseInt(DateTimeUtil.formatDateToStr(DateUtil.convert(bizEventMessageVO.getExtrData().getCreateTime()), DataTimeFormat.DataTimeFormat26.getFormat()).replace(":", "")));
                }
                if (StringUtils.isNotBlank(bizEventMessageVO.getExtrData().getLoanSuccessTime())) {
                    bizEventVO.setLoanSuccessTimeHms(Integer.parseInt(DateTimeUtil.formatDateToStr(DateUtil.convert(bizEventMessageVO.getExtrData().getLoanSuccessTime()), DataTimeFormat.DataTimeFormat26.getFormat()).replace(":", "")));
                }

                Map<String, Object> extMap = Optional.ofNullable(bizEventVO.getExt()).orElse(Maps.newHashMap());
                extMap.put("loanAmt", bizEventMessageVO.getExtrData().getAmount());
                extMap.put("term", bizEventMessageVO.getExtrData().getTerm());
                extMap.put("riskPriceType", bizEventMessageVO.getExtrData().getRiskPriceType());
                extMap.put("riskPrice", bizEventMessageVO.getExtrData().getRiskPrice());
                extMap.put("orderNumber",  bizEventMessageVO.getExtrData().getOrderNumber());

                extMap.put("appUserId", bizEventVO.getAppUserId());
                extMap.put("app",  bizEventVO.getApp());
                extMap.put("innerApp",  bizEventVO.getInnerApp());
                extMap.put("triggerDateTimeHms",  bizEventVO.getTriggerDateTimeHms());
                extMap.put("amount",  bizEventVO.getAmount());
                extMap.put("fundSourceType",  bizEventVO.getFundSourceType());
                extMap.put("firstLoan",  bizEventVO.getFirstLoan());
                extMap.put("cnlPdCode",  bizEventVO.getCnlPdCode());
                extMap.put("createTimeHms",  bizEventVO.getCreateTimeHms());
                extMap.put("loanSuccessTimeHms",  bizEventVO.getLoanSuccessTimeHms());
                extMap.put("status",  bizEventVO.getStatus());
                extMap.put("failedReason",  bizEventVO.getFailedReason());
                bizEventVO.setExt(extMap);
            }
            if (StringUtils.equalsIgnoreCase("UserLogOff", bizEventMessageVO.getBizEventType())) {
                bizEventVO.setSource(bizEventMessageVO.getExtrData().getSource());
            }
            if (StringUtils.equalsIgnoreCase("Notify_IncreaseAmt", bizEventMessageVO.getBizEventType())) {
                BizEventMessageVO.ExtrData extrData = bizEventMessageVO.getExtrData();
                if (extrData != null) {
                    if (extrData.getAmount() != null) {
                        bizEventVO.setAmount(extrData.getAmount());
                    }
                    bizEventVO.setIncreaseAmtStrategyId(extrData.getIncreaseAmtStrategyId());
                    bizEventVO.setIncreaseAmtStatus(extrData.getIncreaseAmtStatus());
                }
            }
            if (StringUtils.equalsIgnoreCase("Notify_LifeRights", bizEventMessageVO.getBizEventType())) {
                bizEventVO.setLifeRightsCallBackStrategyId(bizEventMessageVO.getExtrData().getLifeRightsCallBackStrategyId());
                bizEventVO.setLifeRightCallBackStatus(bizEventMessageVO.getExtrData().getLifeRightCallBackStatus());
            }
            if (StringUtils.equalsIgnoreCase("Notify_XDayInterestFree", bizEventMessageVO.getBizEventType())) {
                bizEventVO.setXDayInterestFreeCallBackStrategyId(bizEventMessageVO.getExtrData().getXDayInterestFreeCallBackStrategyId());
                bizEventVO.setXDayInterestFreeCallBackStatus(bizEventMessageVO.getExtrData().getXDayInterestFreeCallBackStatus());
            }
            if (StringUtils.equalsIgnoreCase("ExtractQuotaCardGuide", bizEventMessageVO.getBizEventType())) {
                bizEventVO.setPriceGroup(bizEventMessageVO.getExtrData().getPriceGroup());
                bizEventVO.setUseManageType(bizEventMessageVO.getExtrData().getUseManageType());
                bizEventVO.setAdjustmentStatus(bizEventMessageVO.getExtrData().getAdjustmentStatus());
                bizEventVO.setExpectedAdvanceAmount(new BigDecimal(bizEventMessageVO.getExtrData().getExpectedAdvanceAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                bizEventVO.setExtractQuotaCardPurchased(Objects.equals(bizEventMessageVO.getExtrData().getUseTriggerType(), "user") ? 1 : 0);
            }

            if (StringUtils.equalsIgnoreCase("RiskBatchIncreaseCredit", bizEventMessageVO.getBizEventType())) {
                bizEventVO.setApiBatchAdjOutput(Objects.equals(bizEventMessageVO.getExtrData().getDecisionResult().getApiBatchAdjOutput(), "1") ? 1 : 0);
                String preModifyAmountPriceGroupOutput = bizEventMessageVO.getExtrData().getDecisionResult().getPreModifyAmountPriceGroupOutput();
                bizEventVO.setIfExtractQuotaCardTrigger(StringUtils.isBlank(preModifyAmountPriceGroupOutput) || Objects.equals(preModifyAmountPriceGroupOutput, "none") ? 0 : 1);
            }
            if (StringUtils.equalsIgnoreCase("OrderPayMember", bizEventMessageVO.getBizEventType())) {
                bizEventVO.setOrderPayStatus(bizEventMessageVO.getExtrData().getOrderPayStatus());
                bizEventVO.setCurrentDeductNumber(bizEventMessageVO.getExtrData().getCurrentDeductNumber());
                bizEventVO.setOrderPayAmount(bizEventMessageVO.getExtrData().getOrderPayAmount().divide(new BigDecimal("100"), 0, RoundingMode.HALF_UP));
                long between = ChronoUnit.DAYS.between(bizEventMessageVO.getExtrData().getFirstDeductExecutionTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        bizEventMessageVO.getExtrData().getCompleteTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
                bizEventVO.setCompleteTimeDistanceFirstDay(new Long(between).intValue());
                DateTimeUtil.formatDateToStr(new Date(), DataTimeFormat.DataTimeFormat26.getFormat());
                bizEventVO.setCompleteTimeHms(Integer.parseInt(DateTimeUtil.formatDateToStr(new Date(), DataTimeFormat.DataTimeFormat26.getFormat()).replace(":", "")));
            }

            if (StringUtils.equalsIgnoreCase("AppLoginIncreaseCredit", bizEventMessageVO.getBizEventType())) {
                //分转元
                bizEventVO.setAppLoginIncreaseAvailableAmount(bizEventMessageVO.getExtrData().getAppLoginIncreaseAvailableAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                bizEventVO.setAppLoginIncreaseAdjustAmount(bizEventMessageVO.getExtrData().getAppLoginIncreaseAdjustAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            if (StringUtils.equalsIgnoreCase("CxhRepayXyf01IncreaseCredit", bizEventMessageVO.getBizEventType())) {
                bizEventVO.setCxhRepayXyf01IncreaseAdjustResult(bizEventMessageVO.getExtrData().getCxhRepayXyf01IncreaseAdjustResult());
                //分转元
                bizEventVO.setCxhRepayXyf01IncreaseAdjustAmount(bizEventMessageVO.getExtrData().getCxhRepayXyf01IncreaseAdjustAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            if (StringUtils.equalsIgnoreCase("RcsptIncreaseCredit", bizEventMessageVO.getBizEventType())) {
                bizEventVO.setManageType(bizEventMessageVO.getExtrData().getManageType());
                bizEventVO.setIncreaseCreditResult(bizEventMessageVO.getExtrData().getIncreaseCreditResult());
                bizEventVO.setTemporaryQuotaInvalidTime(bizEventMessageVO.getExtrData().getEnd_time());

                //分转元
                bizEventVO.setAdjustAmount(bizEventMessageVO.getExtrData().getAdjustAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                bizEventVO.setAvailableAmount(bizEventMessageVO.getExtrData().getAvailableAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            if (StringUtils.equalsIgnoreCase("Notify_VcSign", bizEventMessageVO.getBizEventType())) {
                bizEventVO.setVcSignType(bizEventMessageVO.getExtrData().getVcSignType());
                bizEventVO.setVcSignIsRenew(bizEventMessageVO.getExtrData().getVcSignIsRenew());
                bizEventVO.setVcSignCardType(bizEventMessageVO.getExtrData().getVcSignCardType());
                bizEventVO.setVcSignOrderPrice(new BigDecimal(bizEventMessageVO.getExtrData().getVcSignOrderPrice()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                bizEventVO.setVcDeductTimeHms(Integer.parseInt(DateTimeUtil.formatDateToStr(bizEventMessageVO.getExtrData().getVcDeductTime(), DataTimeFormat.DataTimeFormat26.getFormat()).replace(":", "")));
                bizEventVO.setIsRenewActivity(bizEventMessageVO.getExtrData().getIsRenewActivity());
                bizEventVO.setIsVipFund(bizEventMessageVO.getExtrData().getIsVipFund());
            }
            if (StringUtils.equalsIgnoreCase("LendtradeDerate", bizEventMessageVO.getBizEventType())) {
                bizEventVO.setOriLoanAmt(bizEventMessageVO.getExtrData().getOriLoanAmt());
                bizEventVO.setLoanAmt(bizEventMessageVO.getExtrData().getLoanAmt());
                bizEventVO.setExpirationHour(bizEventMessageVO.getExtrData().getExpirationHour());
                bizEventVO.setFreezeType(bizEventMessageVO.getExtrData().getFreezeType());
            }

            if (StringUtils.equalsIgnoreCase("SurpriseRight", bizEventMessageVO.getBizEventType())) {
                if (bizEventMessageVO.getExtrData()!=null){
                    bizEventVO.setSurpriseRightType(bizEventMessageVO.getExtrData().getSurpriseRightType());
                    if (bizEventMessageVO.getExtrData().getSurpriseRightLimitAmtCardAmount() != null) {
                        //分转元
                        bizEventVO.setSurpriseRightLimitAmtCardAmount(bizEventMessageVO.getExtrData().getSurpriseRightLimitAmtCardAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                    }
                    if (bizEventMessageVO.getExtrData().getSurpriseRightVipFund() != null) {
                        //分转元
                        bizEventVO.setSurpriseRightVipFund(bizEventMessageVO.getExtrData().getSurpriseRightVipFund().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                    }
                }
            }
            bizEventVO.setExtrData(JSON.toJSONString(bizEventMessageVO.getExtrData()));
        }
        if (bizEventVO.getAppUserId() != null) {
            // UID后两位
            bizEventVO.setAppUserIdLast2(Integer.valueOf(CharSequenceUtil.subSufByLength(String.valueOf(bizEventVO.getAppUserId()), 2)));
        }
        if (StringUtils.equalsIgnoreCase("LoanCommit", bizEventMessageVO.getBizEventType())) {
            bizEventVO.setLoanCommitTimeHms(Integer.parseInt(DateTimeUtil.formatDateToStr(DateUtil.convert(bizEventMessageVO.getTriggerDatetime()), DataTimeFormat.DataTimeFormat26.getFormat()).replace(":", "")));
            if (StringUtils.isNotBlank(bizEventMessageVO.getInnerApp())) {
                if (Arrays.asList("xyf01", "fxk", "cxh").contains(bizEventMessageVO.getInnerApp())) {
                    bizEventVO.setInnerAppChannelType("APP");
                } else {
                    bizEventVO.setInnerAppChannelType("API");
                }
            }
        }
        if (StringUtils.equalsAny(bizEventMessageVO.getBizEventType(), EVENT_CREDENTIAL_STUFFING_APPROVED.getEventType(), EVENT_CREDENTIAL_STUFFING_NOT_APPROVED.getEventType())) {
            bizEventVO.setCredentialStuffingResult(bizEventMessageVO.getExtrData().getCredentialStuffingResult());
        }
        if (StringUtils.equalsAny(bizEventMessageVO.getBizEventType(), EVENT_API_CREDENTIAL_STUFFING.getEventType())) {
            bizEventVO.setCheckType(bizEventMessageVO.getExtrData().getCheckType());
            bizEventVO.setCheckTime(bizEventMessageVO.getExtrData().getCheckTime());
            bizEventVO.setApiCredentialStuffingResult(bizEventMessageVO.getExtrData().getApiCredentialStuffingResult());
            if (bizEventMessageVO.getExtrData().getHasLoanSuccRecord() != null) {
                bizEventVO.setHasLoanSuccRecord(bizEventMessageVO.getExtrData().getHasLoanSuccRecord());
            }
            if (bizEventMessageVO.getExtrData().getDataSource() != null) {
                bizEventVO.setDataSource(bizEventMessageVO.getExtrData().getDataSource());
            }
        }
        if (StringUtils.equalsAny(bizEventMessageVO.getBizEventType(), DISTRIBUTION_GRANTED_SUCCESS.getEventType())) {
            bizEventVO.setProduct(bizEventMessageVO.getExtrData().getProduct());
            bizEventVO.setOrderNo(bizEventMessageVO.getExtrData().getOrderNo());
        }
        if (StringUtils.equalsAny(bizEventMessageVO.getBizEventType(), ACCESS_CONTROL_DIVERSION.getEventType())) {
            bizEventVO.setBizType(bizEventMessageVO.getExtrData().getBizType());
            bizEventVO.setLoanType(bizEventMessageVO.getExtrData().getLoanType());
            bizEventVO.setForbiddenApplyDay(bizEventMessageVO.getExtrData().getForbiddenApplyDay());
            bizEventVO.setDiversionLabel(bizEventMessageVO.getExtrData().getDiversionLabel());
            bizEventVO.setAccessAction(bizEventMessageVO.getExtrData().getAccessAction());
        }
        if (StringUtils.equalsAny(bizEventMessageVO.getBizEventType(), SETTLE_SUCCESS.getEventType(), REPAY_SUCCESS.getEventType())) {
            bizEventVO.setPayType(bizEventMessageVO.getExtrData().getPayType());
        }
        if (StringUtils.equalsIgnoreCase("NewRiskTempCredit", bizEventMessageVO.getBizEventType())) {
            bizEventVO.setManageType(bizEventMessageVO.getExtrData().getManageType());
            bizEventVO.setTemporaryQuotaInvalidTime(bizEventMessageVO.getExtrData().getEnd_time());
            bizEventVO.setTemporaryQuotaEffectiveTime(bizEventMessageVO.getExtrData().getStart_time());

            //分转元
            bizEventVO.setAdjustAmount(bizEventMessageVO.getExtrData().getAdjustAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            bizEventVO.setAmount(bizEventMessageVO.getExtrData().getAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        }
        if (StringUtils.equalsIgnoreCase("NewRegister-useractionlog", bizEventMessageVO.getBizEventType())) {
            if (bizEventMessageVO.getExtrData()!=null){
                if (bizEventMessageVO.getExtrData().getUserRegisterSource() != null){
                    bizEventVO.setUserRegisterSource(bizEventMessageVO.getExtrData().getUserRegisterSource());
                }
                if (bizEventMessageVO.getExtrData().getEquityType() != null){
                    bizEventVO.setEquityType(bizEventMessageVO.getExtrData().getEquityType());
                }
            }
        }
        if (StringUtils.equalsAny(bizEventMessageVO.getBizEventType(), PRE_LOAN_SUBMISSION_SUCCESSFUL.getEventType())) {
            if (bizEventMessageVO.getExtrData().getAdvanceLoanScene() != null){
                bizEventVO.setAdvanceLoanScene(bizEventMessageVO.getExtrData().getAdvanceLoanScene());
            }
            if (bizEventMessageVO.getExtrData().getOrderNo() != null){
                bizEventVO.setOrderNo(bizEventMessageVO.getExtrData().getOrderNo());
                Map<String, Object> extMap = Optional.ofNullable(bizEventVO.getExt()).orElse(Maps.newHashMap());
                extMap.put("order_no", bizEventMessageVO.getExtrData().getOrderNo());
                extMap.put("app",  bizEventVO.getApp());
                extMap.put("advanceLoanScene",  bizEventMessageVO.getExtrData().getAdvanceLoanScene());
                bizEventVO.setExt(extMap);
            }
        }
        if (StringUtils.equalsAny(bizEventMessageVO.getBizEventType(), AI_CALL.getEventType())) {
            if (bizEventMessageVO.getExtrData().getCallStatus() != null){
                bizEventVO.setCallStatus(bizEventMessageVO.getExtrData().getCallStatus());
            }
        }
        if (StringUtils.equalsAny(bizEventMessageVO.getBizEventType(), API_ENTRY.getEventType())) {
            if(StringUtils.isNotBlank(bizEventMessageVO.getInnerApp())){
                bizEventVO.setInnerApp(bizEventMessageVO.getInnerApp());
            }
            if (bizEventMessageVO.getExtrData().getApiEntryResult() != null){
                bizEventVO.setApiEntryResult(bizEventMessageVO.getExtrData().getApiEntryResult());
            }
        }
        if (StringUtils.equalsIgnoreCase(ORDER_RISK_PRE_FAILED.getEventType(), bizEventMessageVO.getBizEventType())) {
            Map<String, Object> extMap = Optional.ofNullable(bizEventVO.getExt()).orElse(Maps.newHashMap());
            extMap.put("inner_app",  bizEventVO.getInnerApp());
            extMap.put("innerApp",  bizEventVO.getInnerApp());
            extMap.put("app",  bizEventVO.getApp());
            bizEventVO.setExt(extMap);
        }
        if (StringUtils.equalsAny(RECEIVED_COUPON.getEventType(),bizEventMessageVO.getBizEventType())) {
            if(StringUtils.isNotBlank(bizEventMessageVO.getInnerApp())){
                bizEventVO.setInnerApp(bizEventMessageVO.getInnerApp());
            }
            if (bizEventMessageVO.getExtrData().getCouponType() != null){
                bizEventVO.setCouponType(bizEventMessageVO.getExtrData().getCouponType());
            }
            if (bizEventMessageVO.getExtrData().getCouponBelongsTo() != null){
                bizEventVO.setCouponBelongsTo(bizEventMessageVO.getExtrData().getCouponBelongsTo());
            }
        }
        if (StringUtils.equalsAny(TELE_INVENTORY.getEventType(),bizEventMessageVO.getBizEventType())) {
            if (bizEventMessageVO.getExtrData().getInventoryStatus() != null){
                bizEventVO.setInventoryStatus(bizEventMessageVO.getExtrData().getInventoryStatus());
            }
        }
        if (StringUtils.equalsAny(ORDER_RISK_PASSED.getEventType(),bizEventMessageVO.getBizEventType())) {
            if (StringUtils.isNotBlank(bizEventMessageVO.getEventDatetime())) {
                bizEventVO.setRiskPassedTimeHms(Integer.parseInt(DateTimeUtil.formatDateToStr(DateUtil.convert(bizEventMessageVO.getEventDatetime()), DataTimeFormat.DataTimeFormat26.getFormat()).replace(":", "")));
            }
        }
            log.info("转换实体内容：{}", bizEventVO);
        return bizEventVO;
    }

    /**
     * 拼装表达式
     *
     * @param marketSubEventList 事件集合
     * @return 表达式
     */
    protected String getExpression(List<StrategyMarketSubEventDo> marketSubEventList) {
        return marketSubEventList.stream().map(StrategyMarketSubEventDo::getExpression).collect(Collectors.joining(" && "));
    }

    /**
     * 获取表达式入参
     *
     * @param bizEventVO         消息体
     * @param marketSubEventList 子事件
     * @return 表达式入参
     */
    protected Map<String, Object> getExpressionParam(BizEventVO bizEventVO, List<StrategyMarketSubEventDo> marketSubEventList) {
        Map<String, Object> param = new HashMap<>();
        marketSubEventList.stream().map(StrategyMarketSubEventDo::getEventName).forEach(eventName -> {
            EventMetaDataDo eventMetaData = eventMetaDataService.selectByEventName(eventName);
            ValueTypeEnum valueTypeEnum = ValueTypeEnum.getInstance(eventMetaData.getLabelValueType());
            param.put(eventName, valueTypeEnum.normalizeValue(getParamValue(bizEventVO, eventName)));
        });
        return param;
    }

    /**
     * 从解析后的实体中获取事件名称对应的参数值，兼容模式：先从实体类中反射获取，获取不到从扩展的extMap中获取
     * @param bizEventVO 解析后的实体信息
     * @param eventName 属性名称
     * @return 属性对应的值
     */
    private Object getParamValue(BizEventVO bizEventVO, String eventName){
        Map<String,Object> extMap = bizEventVO.getExt();
        if(!CollectionUtils.isEmpty(extMap)){
            return extMap.get(eventName);
        }else {
            return ReflectGetFieldUtil.getFieldValue(bizEventVO, eventName);
        }
    }

    /**
     * 人群包筛选
     *
     * @param eventContext 事件消息
     * @return 筛选结果
     */
    protected Pair<Boolean, CrowdDetailDo> crowdPackVerify(StrategyEventCheckContext eventContext,Map<Long, CrowdContext> crowdContent) {
        // 拿到状态为刷新成功或已完成的人群表ID
        List<Long> crowdPackIds = Arrays.stream(eventContext.getStrategyDo().getCrowdPackId().split(";")).map(Convert::toLong).collect(Collectors.toList());

        // 人群包ID不为空，查询人群包
        if (!CollectionUtils.isEmpty(crowdPackIds)) {
            Long appUserId = eventContext.getBizEventVO().getAppUserId();
//            Long randomNumber = randomNumService.generateRandomNumber(eventContext.getStrategyDo().getBizKey(), appUserId, 2);
            if ( !crowdPackService.verifyCrowdPackByNewRandom(appUserId,
                    crowdContent.values().stream().map(CrowdContext::getCrowdPack).collect(Collectors.toList()))) {
                return Pair.of(Boolean.FALSE, null);
            }

            // 是否通过洞察平台接口判断
            if (crowdInfoService.isCheckByInsightPlatform(crowdPackIds)) {
                return crowdInfoService.checkByInsightPlatform(crowdPackIds, appUserId);
            } else {
                // 查询人群包所在的表
                List<Triple<Long, Long, List<String>>> triples = crowdPackService.getExecLogIdAndTablePairList(crowdPackIds,crowdContent);
                if (CollectionUtils.isEmpty(triples)) {
                    log.warn("未查询到人群明细表,人群包ID:{}", CollUtil.join(crowdPackIds, ","));
                    return Pair.of(null, null);
                }

                // 检查是否存在
                if (ApolloUtil.switchStatus("ads.hasUserRecord.byIn") && checkCrowdExecTimeSameDay(triples)) {
                    Set<Long> crowdIds = triples.stream().map(Triple::getLeft).collect(Collectors.toSet());
                    Triple<Long, Long, List<String>> triple = triples.get(0);
                    Long crowdExecLogId = triple.getMiddle();
                    String tableName = triple.getRight().get(0);
                    CrowdDetailDo crowdDetailDo = crowdPackService.hasUserRecordByIn(crowdIds, appUserId, crowdExecLogId, tableName);
                    if (crowdDetailDo != null) {
                        log.info("hasUserRecordByIn 匹配到人群包:{}, crowdDetail={}", triple.getLeft(), JSON.toJSONString(crowdDetailDo));
                        Tracer.logEvent("StarRocksHasUserRecord", "byIn-hit");
                        return Pair.of(Boolean.TRUE, crowdDetailDo);
                    }
                    Tracer.logEvent("StarRocksHasUserRecord", "byIn-notHit");
                } else {
                    for (Triple<Long, Long, List<String>> triple : triples) {
                        for (String tableName : triple.getRight()) {
                            Optional<CrowdDetailDo> detailOptional = crowdPackService.hasUserRecord(triple.getLeft(),appUserId, triple.getMiddle(), tableName);
                            if (detailOptional.isPresent()) {
                                log.info("hasUserRecordByFor 匹配到人群包:{}, crowdDetail={}", triple.getLeft(),  JSON.toJSONString(detailOptional.get()));
                                Tracer.logEvent("StarRocksHasUserRecord", "byFor-hit");
                                return Pair.of(Boolean.TRUE, detailOptional.get());
                            }
                        }
                        Tracer.logEvent("StarRocksHasUserRecord", "byFor-notHit");
                    }
                }
            }
        } else {
            log.warn("不存在状态为[刷新成功]或[已完成]的人群包");
        }
        return Pair.of(Boolean.FALSE, null);
    }

    /**
     * 检查人群包分区是否在同一天 且 都是标签圈选人群包
     * @param triples
     * @return
     */
    private boolean checkCrowdExecTimeSameDay(List<Triple<Long, Long, List<String>>> triples) {
        try {
            if (triples.size() <= 1) {
                return false;
            }

            Set<LocalDate> localDateSet = Sets.newHashSet();
            for (Triple<Long, Long, List<String>> triple : triples) {
                List<String> crowdTables = Optional.ofNullable(triple.getRight()).orElse(Lists.newArrayList());
                if (crowdTables.size() > 1 || !crowdTables.contains(adsConfig.getAdsTable())) {
                    return false;
                }

                LocalDateTime finishTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(triple.getMiddle()), ZoneId.systemDefault());
                localDateSet.add(finishTime.toLocalDate());
            }
            boolean result = Objects.equals(localDateSet.size(), 1);
            log.info("AbstractStrategyEventDispatchService checkCrowdExecTimeSameDay triples={} localDateSet={} result={}", JSON.toJSONString(triples), JSON.toJSONString(localDateSet), result);
            return result;
        } catch (Exception e) {
            log.error("AbstractStrategyEventDispatchService checkCrowdExecTimeSameDay error={}", e.getMessage(), e);
        }
        return false;
    }

    /**
     * 截止本次营销前
     *
     * @param eventCondition 标签配置
     * @return startTime，格式：yyyy-MM-dd HH:mm:ss
     */
    protected String getStartTime(StrategyMarketEventConditionDo eventCondition) {
        LocalDateTime startTime = LocalDateTime.now();
        if (Objects.nonNull(eventCondition)) {
            switch (Optional.ofNullable(eventCondition.getTimeType()).orElse(3)) {
                case 1:
                    startTime = startTime.plusMinutes(-eventCondition.getTimeValue());
                    break;
                case 2:
                    startTime = startTime.plusHours(-eventCondition.getTimeValue());
                    break;
                default:
                    startTime = LocalDate.now().atStartOfDay();
            }
        }
        return LocalDateTimeUtil.format(startTime, TimeFormat.DATE_TIME);
    }

    /**
     * 根据分组配置过滤
     *
     * @param strategyDo      策略
     * @param crowdDetail     用户明细
     * @param strategyGroupDo 分组配置
     * @return 匹配结果
     */
    protected boolean matchGroup(StrategyDo strategyDo, CrowdDetailDo crowdDetail, StrategyGroupDo strategyGroupDo) {
        BiPredicate<String, Integer> matchFun = strategyGroupDo.match(StrategyGroupTypeEnum.getInstance(strategyDo.getAbType()));
        List<CrowdDetailDo> matchList = strategyGroupService.matchGroupRule(strategyDo.getBizKey(), matchFun, Collections.singletonList(crowdDetail));
        return !CollectionUtils.isEmpty(matchList);
    }

    /**
     * 流控
     *
     * @param messageId       消息ID
     * @param triggerDatetime 触发时间
     * @param channelDo       渠道
     * @param crowdDetail     用户明细
     * @return 流控结果
     */
    public List<CrowdDetailDo> flowCtrl(String messageId, LocalDateTime triggerDatetime, StrategyMarketChannelDo channelDo, CrowdDetailDo crowdDetail, List<Integer> statusList,String bizEventType) {
        // 查询当前策略的业务线配置
        String bizType = getBizType(channelDo.getStrategyId());
        List<FlowCtrlDo> flowCtrlRule = flowCtrlCoreService.getFlowCtrlRule(channelDo.getStrategyId(), channelDo.getMarketChannel(), bizType);
        FlowCtrlDto flowCtrlDto = new FlowCtrlDto();
        flowCtrlDto.setTableNo(getTableNo(triggerDatetime));
        flowCtrlDto.setMarketChannelDo(channelDo);
        flowCtrlDto.setList(Collections.singletonList(crowdDetail));
        flowCtrlDto.setFlowCtrlRuleList(flowCtrlRule);
        flowCtrlDto.setMessageId(messageId);
        flowCtrlDto.setTriggerDatetime(triggerDatetime);
        flowCtrlDto.setStrategyRulerEnum(StrategyRulerEnum.EVENT);
        flowCtrlDto.setBizEventType(bizEventType);
        return flowCtrlCoreService.flowCtrl(flowCtrlDto, statusList);
    }

    /**
     * 封装用户明细
     *
     * @param bizEventVO 事件消息
     * @return 用户明细
     */
    protected CrowdDetailDo convertToCrowdDetail(BizEventVO bizEventVO) {
        CrowdDetailDo crowdDetail = new CrowdDetailDo();
        crowdDetail.setCrowdId(bizEventVO.getCrowdPackId());
        crowdDetail.setUserId(bizEventVO.getAppUserId());
        crowdDetail.setMobile(bizEventVO.getMobile());
        crowdDetail.setApp(bizEventVO.getApp());
        crowdDetail.setInnerApp(bizEventVO.getInnerApp());
        crowdDetail.setAbNum(bizEventVO.getAbNum());
        crowdDetail.setAppUserIdLast2(bizEventVO.getAppUserIdLast2());
        return crowdDetail;
    }

    /**
     * 封装下发请求参数
     * 下发bizType
     * @param triple 策略分组、渠道、执行日志
     * @return 下发请求参数
     */
    protected DispatchDto convertToDispatchDto(BizEventVO eventVO, Triple<StrategyGroupDo, StrategyMarketChannelDo, StrategyExecLogDo> triple) {
        DispatchDto dispatch = new DispatchDto();
        dispatch.setStrategyExecId(eventVO.getStrategyExecId());
        dispatch.setDetailTableNo(getTableNo(eventVO.getTriggerDatetime()));
        dispatch.setStrategyId(triple.getLeft().getStrategyId());
        dispatch.setBizType(getBizType(triple.getLeft().getStrategyId()));
        dispatch.setStrategyGroupId(triple.getLeft().getId());
        dispatch.setStrategyGroupName(triple.getLeft().getName());
        dispatch.setStrategyChannelId(triple.getMiddle().getId());
        dispatch.setStrategyChannelXxlJobId(triple.getMiddle().getXxlJobId());
        dispatch.setStrategyChannel(triple.getMiddle().getMarketChannel());
        dispatch.setStrategyMarketChannelTemplateId(triple.getMiddle().getTemplateId());
        dispatch.setNameTypeId(triple.getMiddle().getTemplateId());
        dispatch.setStrategyExecLogId(triple.getRight().getId());
        dispatch.setStrategyExecLogRetryId(triple.getRight().getRetryId());
        dispatch.setMessageId(eventVO.getMessageId());
        dispatch.setTriggerDatetime(eventVO.getTriggerDatetime());
        dispatch.setStrategyRulerEnum(StrategyRulerEnum.EVENT);
        dispatch.setBizEventType(eventVO.getBizEventType());
        dispatch.setStrategyMarketChannelDo(triple.getMiddle());
        if(!Objects.isNull(triple.getMiddle().getExtInfo())){
            Map extInfo = JSONObject.parseObject(triple.getMiddle().getExtInfo(),Map.class);
            if(!Objects.isNull(extInfo) && extInfo.containsKey("policyId")){
                String policyId = String.valueOf(extInfo.get("policyId"));
                dispatch.setStrategyMarketChannelTemplateId(policyId);
            }
            if(!Objects.isNull(extInfo) && extInfo.containsKey("activityId")){
                String activityId = String.valueOf(extInfo.get("activityId"));
                dispatch.setActivityId(activityId);
            }
        }
        // 提额策略
        if (dispatch.getStrategyChannel() ==
                StrategyMarketChannelEnum.INCREASE_AMOUNT.getCode()){
            IncreaseAmtDto increaseAmtDto = JsonUtil.parse(triple.getMiddle().getExtInfo(),
                    IncreaseAmtDto.class);
            if (increaseAmtDto == null){
                log.error("策略提额配置信息异常, 策略id:{}", dispatch.getStrategyId());
                throw new StrategyException("策略提额配置信息异常");
            }
            try {
                IncreaseAmtDto.IncreaseAmtConfig increaseAmtConfig = increaseAmtDto.getIncreaseAmtConfig();
                String increaseType = increaseAmtConfig.getIncreaseType();
                IncreaseAmtParamDto increaseAmtParamDto;
                if (StringUtils.equalsAny(increaseType, IncreaseAmtDto.IncreaseType.TEMP_CREDIT, IncreaseAmtDto.IncreaseType.FIXED_CREDIT,
                        IncreaseAmtDto.IncreaseType.PERSONAL_MARKETING_RELOAN_TEMP)) {
                    increaseAmtParamDto = increaseAmtConfig.getParamDto();
                    increaseAmtParamDto.setIncreaseType(increaseType);
                    if (!increaseAmtParamDto.isValid()){
                        throw new StrategyException("策略提额配置信息异常");
                    }
                    dispatch.setIncreaseAmtParamDto(increaseAmtParamDto);
                } else if (StringUtils.equalsAny(increaseType, IncreaseAmtDto.IncreaseType.PERSONAL_API_FST_LOGIN_TEMP, IncreaseAmtDto.IncreaseType.LOAN_UPTO_FULLAMT)) {
                    increaseAmtParamDto = new IncreaseAmtParamDto();
                    increaseAmtParamDto.setIncreaseType(increaseType);
                    dispatch.setIncreaseAmtParamDto(increaseAmtParamDto);
                }
            }catch (Exception ex){
                log.error("策略提额配置信息异常, 策略id:{}, error:{}", dispatch.getStrategyId(), ex.getMessage(), ex);
                throw new StrategyException("策略提额配置信息异常");
            }
        }
        //ai
        if (dispatch.getStrategyMarketChannelDo().getMarketChannel() ==
                StrategyMarketChannelEnum.AI_PRONTO.getCode()) {
            AiProntoChannelDto aiProntoChannelDto = JsonUtil.parse(dispatch.getStrategyMarketChannelDo().getExtInfo(),
                    AiProntoChannelDto.class);
            if (aiProntoChannelDto == null || StringUtils.isBlank(aiProntoChannelDto.getNameTypeId())) {
                log.error("策略ai_pronto配置信息异常, 策略id:{}", dispatch.getStrategyId());
                throw new StrategyException("策略ai_pronto配置信息异常");
            }
            dispatch.setAiProntoChannelDto(aiProntoChannelDto);
        }
        return dispatch;
    }

    public String getBizType(Long strategyId) {
        StrategyDo strategyDo = cacheStrategyService.selectById(strategyId);
        if(strategyDo != null) {
            return strategyDo.getBusinessType();
        }
        return "";
    }

    /**
     * 封装留白组明细
     *
     * @param crowdDetail 用户明细
     * @param dispatchDto 下发参数
     * @return 留白组明细
     */
    protected UserBlankGroupDetailDo convertToBlankDetail(CrowdDetailDo crowdDetail, DispatchDto dispatchDto) {
        UserBlankGroupDetailDo userBlankGroupDetailDo = new UserBlankGroupDetailDo();
        userBlankGroupDetailDo.setUserId(crowdDetail.getUserId());
        userBlankGroupDetailDo.setMobile(crowdDetail.getMobile());
        userBlankGroupDetailDo.setBatchNum(serialNumberUtil.batchNum());
        userBlankGroupDetailDo.setCrowdPackId(Convert.toLong(crowdDetail.getCrowdId(), -1L));
        userBlankGroupDetailDo.setStrategyId(dispatchDto.getStrategyId());
        userBlankGroupDetailDo.setStrategyChannelId(dispatchDto.getStrategyChannelId());
        userBlankGroupDetailDo.setMarketChannel(dispatchDto.getStrategyChannel());
        userBlankGroupDetailDo.setStrategyExecId(LocalDateTimeUtil.format(LocalDate.now(), "yyMMdd") + String.format("%010d", dispatchDto.getStrategyId()));
        userBlankGroupDetailDo.setStatus(UserDispatchDetailStatusEnum.SUCCESS.getStatus());
        userBlankGroupDetailDo.setDispatchTime(LocalDateTime.now());
        userBlankGroupDetailDo.setMessageId(dispatchDto.getMessageId());
        userBlankGroupDetailDo.setTriggerDatetime(dispatchDto.getTriggerDatetime());
        userBlankGroupDetailDo.setBizEventType(dispatchDto.getBizEventType());
        userBlankGroupDetailDo.setStrategyGroupId(dispatchDto.getStrategyGroupId());
        userBlankGroupDetailDo.setStrategyGroupName(dispatchDto.getStrategyGroupName());
        return userBlankGroupDetailDo;
    }

    /**
     * 保存通过筛选发送失败的用户记录
     *
     * @param crowdDetail 用户信息
     * @param dispatchDto 下发信息
     */

    protected void saveFailRecord(CrowdDetailDo crowdDetail, DispatchDto dispatchDto) {
        saveFailRecord(crowdDetail, dispatchDto, dispatchDto.getFailReason(), null);
    }

    /**
     * 保存通过筛选发送失败的用户记录
     *
     * @param crowdDetail 用户信息
     * @param dispatchDto 下发信息
     */
    protected void saveFailRecord(CrowdDetailDo crowdDetail, DispatchDto dispatchDto, String failReason, String groupName) {
        UserDispatchFailDetailDto dispatchFailDetail = new UserDispatchFailDetailDto();
        dispatchFailDetail.setStrategyId(dispatchDto.getStrategyId());
        dispatchFailDetail.setMarketChannel(dispatchDto.getStrategyChannel());
        dispatchFailDetail.setTemplateNo(Optional.ofNullable(dispatchDto.getStrategyMarketChannelTemplateId()).orElse(""));
        dispatchFailDetail.setGroupName(groupName);
        dispatchFailDetail.setFailReason(failReason);
        dispatchFailDetail.setList(Collections.singletonList(Optional.ofNullable(crowdDetail).map(crowdDetailDo -> {
            UserDispatchFailDetailDto.UserInfo userInfo = new UserDispatchFailDetailDto.UserInfo();
            userInfo.setUserId(crowdDetailDo.getUserId());
            userInfo.setApp(crowdDetailDo.getApp());
            userInfo.setMobile(crowdDetailDo.getMobile());
            return userInfo;
        }).orElse(null)));
        dispatchFailDetail.setBizEventType(dispatchDto.getBizEventType());
        if (StringUtils.isNotEmpty(dispatchDto.getDispatchType())) {
            dispatchFailDetail.setDispatchType(dispatchDto.getDispatchType());
        }
        try {
            userDispatchFailDetailService.batchSave(dispatchFailDetail);
        } catch (Exception ex) {
            log.error("saveFailRecord error", ex);
        }
    }

    /**
     * 设置渠道执行日志数量
     *
     * @param strategyGroupId 分组ID
     * @param strategyChannel 渠道
     * @param sendCount       发送数量
     */
    protected void setChannelExecLogCount(Long strategyGroupId, Integer strategyChannel, Integer sendCount) {
        String date = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");

        String sendCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_SEND_COUNT, date, strategyGroupId, strategyChannel);
        redisUtils.increment(sendCountKey, Convert.toLong(sendCount));

        String execCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_EXEC_COUNT, date, strategyGroupId, strategyChannel);
        redisUtils.increment(execCountKey, 1L);
    }

    /**
     * 被过滤组数量统计（预筛）
     *
     * @param strategyId 执行日志ID
     * @param type       类型 1-实时标签 2-排除项
     */
    protected void setFilterExecLogCount(Long strategyId, Integer type) {
        String date = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");
        if (type == 1) {
            String sendCountKey = String.format(RedisKeyConstants.EVENT_FILTER_EXEC_LOG_SEND_COUNT, date, strategyId);
            redisUtils.increment(sendCountKey, 1L);
        } else {
            String receiveCountKey = String.format(RedisKeyConstants.EVENT_FILTER_EXEC_LOG_RECEIVE_COUNT, date, strategyId);
            redisUtils.increment(receiveCountKey, 1L);
        }
    }

    /**
     * 递归获取执行日志
     *
     * @param logKey 执行日志缓存key
     * @return 执行日志
     */
    protected StrategyExecLogDo queryRedisStrategyExecLog(String logKey) {
        if (redisUtils.hasKey(logKey)) {
            String strategyExecLogJson = redisUtils.get(logKey);
            if (StringUtils.isNotBlank(strategyExecLogJson)) {
                return JSON.parseObject(strategyExecLogJson, StrategyExecLogDo.class);
            }
        }
        ThreadUtil.sleep(200);
        return queryRedisStrategyExecLog(logKey);
    }

    /**
     * 获取明细表序号
     *
     * @return 明细表序号
     */
    protected String getTableNo(LocalDateTime triggerDatetime) {
        return LocalDateTimeUtil.format(triggerDatetime, "yyyyMM");
    }

    /**
     * 时长
     *
     * @param startTime 开始时间
     * @return 耗时，单位：ms
     */
    protected long duration(long startTime) {
        return Instant.now().toEpochMilli() - startTime;
    }
}
