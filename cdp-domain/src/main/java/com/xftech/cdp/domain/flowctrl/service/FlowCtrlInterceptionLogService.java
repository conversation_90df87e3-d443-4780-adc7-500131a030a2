package com.xftech.cdp.domain.flowctrl.service;

import com.xftech.cdp.domain.flowctrl.model.dto.FlowCtrlDto;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlInterceptionLogDo;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/29 16:46
 */
public interface FlowCtrlInterceptionLogService {

    /**
     * 保存拦截日志
     */
    void saveInterceptionLog(FlowCtrlDto flowCtrlDto, List<ImmutablePair<Long, Long>> flowCtrlRefuseList);

    void saveInterceptionLogNew(FlowCtrlDto flowCtrlDto, List<ImmutablePair<Long, Long>> flowCtrlRefuseList);

    void saveInterceptionLog(FlowCtrlInterceptionLogDo flowCtrlInterceptionLogDo);
}
